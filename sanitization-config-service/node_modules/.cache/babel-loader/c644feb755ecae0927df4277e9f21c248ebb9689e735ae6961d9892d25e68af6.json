{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Shield, Search, Download, Trash2, Eye, EyeOff, Power, PowerOff } from 'lucide-react';\nimport { sanitizationApi } from './services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [globalEnabled, setGlobalEnabled] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const data = await sanitizationApi.getRules();\n      setConfig(data);\n      setGlobalEnabled(data.enabled);\n    } catch (error) {\n      toast.error('Failed to fetch rules');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleGlobalSwitch = async () => {\n    try {\n      const newState = !globalEnabled;\n      await sanitizationApi.toggleGlobalSwitch(newState);\n      setGlobalEnabled(newState);\n      toast.success(`Global sanitization ${newState ? 'enabled' : 'disabled'}`);\n    } catch (error) {\n      toast.error('Failed to toggle global switch');\n    }\n  };\n  const toggleRule = async (ruleId, enabled) => {\n    try {\n      await sanitizationApi.toggleRule(ruleId, enabled);\n      await fetchRules();\n      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'}`);\n    } catch (error) {\n      toast.error('Failed to toggle rule');\n    }\n  };\n  const deleteRule = async ruleId => {\n    if (!window.confirm('Are you sure you want to delete this rule?')) {\n      return;\n    }\n    try {\n      await sanitizationApi.deleteRule(ruleId);\n      await fetchRules();\n      toast.success('Rule deleted successfully');\n    } catch (error) {\n      toast.error('Failed to delete rule');\n    }\n  };\n  const exportRules = async () => {\n    try {\n      const data = await sanitizationApi.exportRules();\n      const blob = new Blob([JSON.stringify(data, null, 2)], {\n        type: 'application/json'\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'sanitization-rules.json';\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      toast.success('Rules exported successfully');\n    } catch (error) {\n      toast.error('Failed to export rules');\n    }\n  };\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()))) || [];\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'text-red-600 bg-red-50';\n      case 'HIGH':\n        return 'text-orange-600 bg-orange-50';\n      case 'MEDIUM':\n        return 'text-yellow-600 bg-yellow-50';\n      case 'LOW':\n        return 'text-green-600 bg-green-50';\n      default:\n        return 'text-gray-600 bg-gray-50';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading sanitization rules...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              className: \"h-8 w-8 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Data Sanitization Rules\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Manage your data privacy and security rules\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleGlobalSwitch,\n              className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${globalEnabled ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-red-100 text-red-700 hover:bg-red-200'}`,\n              children: [globalEnabled ? /*#__PURE__*/_jsxDEV(Power, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(PowerOff, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 66\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: globalEnabled ? 'Enabled' : 'Disabled'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: exportRules,\n              className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 py-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search rules...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6 text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Total: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: (config === null || config === void 0 ? void 0 : config.rules.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Active: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-green-600\",\n                children: (config === null || config === void 0 ? void 0 : config.rules.filter(r => r.enabled).length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Inactive: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-red-600\",\n                children: (config === null || config === void 0 ? void 0 : config.rules.filter(r => !r.enabled).length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Shield, {\n            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: \"No rules found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first sanitization rule.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this) : filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: rule.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(rule.severity)}`,\n                  children: rule.severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full\",\n                  children: rule.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-3\",\n                children: rule.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"ID: \", rule.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Pattern: \", rule.pattern]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 40\n                }, this), rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Fields: \", rule.fieldNames.join(', ')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleRule(rule.id, !rule.enabled),\n                className: `p-2 rounded-lg transition-colors ${rule.enabled ? 'text-green-600 hover:bg-green-50' : 'text-gray-400 hover:bg-gray-50'}`,\n                title: rule.enabled ? 'Disable rule' : 'Enable rule',\n                children: rule.enabled ? /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 39\n                }, this) : /*#__PURE__*/_jsxDEV(EyeOff, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => deleteRule(rule.id),\n                className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                title: \"Delete rule\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)\n        }, rule.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Wxx0otdjYIYQJdGRHAMaayLMi9Y=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Toaster", "Shield", "Search", "Download", "Trash2", "Eye", "Eye<PERSON>ff", "Power", "PowerOff", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "App", "_s", "config", "setConfig", "loading", "setLoading", "globalEnabled", "setGlobalEnabled", "searchTerm", "setSearchTerm", "fetchRules", "data", "getRules", "enabled", "error", "console", "toggleGlobalSwitch", "newState", "success", "toggleRule", "ruleId", "deleteRule", "window", "confirm", "exportRules", "blob", "Blob", "JSON", "stringify", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "filteredRules", "rules", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "getSeverityColor", "severity", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "onClick", "placeholder", "value", "onChange", "e", "target", "length", "r", "map", "pattern", "fieldNames", "join", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport {\n  Shield,\n  Search,\n  Download,\n  Trash2,\n  Eye,\n  EyeOff,\n  Power,\n  PowerOff\n} from 'lucide-react';\nimport { sanitizationApi } from './services/api';\nimport { SanitizationConfig } from './types';\nimport toast from 'react-hot-toast';\n\nfunction App() {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [globalEnabled, setGlobalEnabled] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const data = await sanitizationApi.getRules();\n      setConfig(data);\n      setGlobalEnabled(data.enabled);\n    } catch (error) {\n      toast.error('Failed to fetch rules');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const toggleGlobalSwitch = async () => {\n    try {\n      const newState = !globalEnabled;\n      await sanitizationApi.toggleGlobalSwitch(newState);\n      setGlobalEnabled(newState);\n      toast.success(`Global sanitization ${newState ? 'enabled' : 'disabled'}`);\n    } catch (error) {\n      toast.error('Failed to toggle global switch');\n    }\n  };\n\n  const toggleRule = async (ruleId: string, enabled: boolean) => {\n    try {\n      await sanitizationApi.toggleRule(ruleId, enabled);\n      await fetchRules();\n      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'}`);\n    } catch (error) {\n      toast.error('Failed to toggle rule');\n    }\n  };\n\n  const deleteRule = async (ruleId: string) => {\n    if (!window.confirm('Are you sure you want to delete this rule?')) {\n      return;\n    }\n    \n    try {\n      await sanitizationApi.deleteRule(ruleId);\n      await fetchRules();\n      toast.success('Rule deleted successfully');\n    } catch (error) {\n      toast.error('Failed to delete rule');\n    }\n  };\n\n  const exportRules = async () => {\n    try {\n      const data = await sanitizationApi.exportRules();\n      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'sanitization-rules.json';\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      toast.success('Rules exported successfully');\n    } catch (error) {\n      toast.error('Failed to export rules');\n    }\n  };\n\n  const filteredRules = config?.rules.filter(rule =>\n    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'text-red-600 bg-red-50';\n      case 'HIGH': return 'text-orange-600 bg-orange-50';\n      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50';\n      case 'LOW': return 'text-green-600 bg-green-50';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading sanitization rules...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      \n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <Shield className=\"h-8 w-8 text-blue-600\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Data Sanitization Rules</h1>\n                <p className=\"text-sm text-gray-600\">Manage your data privacy and security rules</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={toggleGlobalSwitch}\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${\n                  globalEnabled\n                    ? 'bg-green-100 text-green-700 hover:bg-green-200'\n                    : 'bg-red-100 text-red-700 hover:bg-red-200'\n                }`}\n              >\n                {globalEnabled ? <Power className=\"h-4 w-4\" /> : <PowerOff className=\"h-4 w-4\" />}\n                <span>{globalEnabled ? 'Enabled' : 'Disabled'}</span>\n              </button>\n              \n              <button\n                onClick={exportRules}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                <Download className=\"h-4 w-4\" />\n                <span>Export</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Search and Stats */}\n      <div className=\"max-w-6xl mx-auto px-4 py-6\">\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search rules...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <div className=\"flex items-center space-x-6 text-sm text-gray-600\">\n              <div>Total: <span className=\"font-semibold text-gray-900\">{config?.rules.length || 0}</span></div>\n              <div>Active: <span className=\"font-semibold text-green-600\">{config?.rules.filter(r => r.enabled).length || 0}</span></div>\n              <div>Inactive: <span className=\"font-semibold text-red-600\">{config?.rules.filter(r => !r.enabled).length || 0}</span></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Rules List */}\n        <div className=\"space-y-4\">\n          {filteredRules.length === 0 ? (\n            <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n              <Shield className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No rules found</h3>\n              <p className=\"text-gray-600\">\n                {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first sanitization rule.'}\n              </p>\n            </div>\n          ) : (\n            filteredRules.map((rule) => (\n              <div key={rule.id} className=\"bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{rule.name}</h3>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(rule.severity)}`}>\n                        {rule.severity}\n                      </span>\n                      <span className=\"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full\">\n                        {rule.type}\n                      </span>\n                    </div>\n                    \n                    <p className=\"text-gray-600 mb-3\">{rule.description}</p>\n                    \n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <span>ID: {rule.id}</span>\n                      {rule.pattern && <span>Pattern: {rule.pattern}</span>}\n                      {rule.fieldNames && rule.fieldNames.length > 0 && (\n                        <span>Fields: {rule.fieldNames.join(', ')}</span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    <button\n                      onClick={() => toggleRule(rule.id, !rule.enabled)}\n                      className={`p-2 rounded-lg transition-colors ${\n                        rule.enabled\n                          ? 'text-green-600 hover:bg-green-50'\n                          : 'text-gray-400 hover:bg-gray-50'\n                      }`}\n                      title={rule.enabled ? 'Disable rule' : 'Enable rule'}\n                    >\n                      {rule.enabled ? <Eye className=\"h-4 w-4\" /> : <EyeOff className=\"h-4 w-4\" />}\n                    </button>\n                    \n                    <button\n                      onClick={() => deleteRule(rule.id)}\n                      className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n                      title=\"Delete rule\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SACEC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,QAAQ,QACH,cAAc;AACrB,SAASC,eAAe,QAAQ,gBAAgB;AAEhD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,IAAI,GAAG,MAAMf,eAAe,CAACgB,QAAQ,CAAC,CAAC;MAC7CT,SAAS,CAACQ,IAAI,CAAC;MACfJ,gBAAgB,CAACI,IAAI,CAACE,OAAO,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,uBAAuB,CAAC;MACpCC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,CAACX,aAAa;MAC/B,MAAMV,eAAe,CAACoB,kBAAkB,CAACC,QAAQ,CAAC;MAClDV,gBAAgB,CAACU,QAAQ,CAAC;MAC1BpB,KAAK,CAACqB,OAAO,CAAC,uBAAuBD,QAAQ,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;IAC3E,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,gCAAgC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAOC,MAAc,EAAEP,OAAgB,KAAK;IAC7D,IAAI;MACF,MAAMjB,eAAe,CAACuB,UAAU,CAACC,MAAM,EAAEP,OAAO,CAAC;MACjD,MAAMH,UAAU,CAAC,CAAC;MAClBb,KAAK,CAACqB,OAAO,CAAC,QAAQL,OAAO,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAOD,MAAc,IAAK;IAC3C,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF,MAAM3B,eAAe,CAACyB,UAAU,CAACD,MAAM,CAAC;MACxC,MAAMV,UAAU,CAAC,CAAC;MAClBb,KAAK,CAACqB,OAAO,CAAC,2BAA2B,CAAC;IAC5C,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMb,IAAI,GAAG,MAAMf,eAAe,CAAC4B,WAAW,CAAC,CAAC;MAChD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACjB,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QAAEkB,IAAI,EAAE;MAAmB,CAAC,CAAC;MACpF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;MACrC,MAAMQ,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;MACZG,CAAC,CAACI,QAAQ,GAAG,yBAAyB;MACtCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;MAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MACxBjC,KAAK,CAACqB,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,wBAAwB,CAAC;IACvC;EACF,CAAC;EAED,MAAM6B,aAAa,GAAG,CAAAzC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0C,KAAK,CAACC,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CACzD,CAAC,KAAI,EAAE;EAEP,MAAMI,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,MAAM;QAAE,OAAO,8BAA8B;MAClD,KAAK,QAAQ;QAAE,OAAO,8BAA8B;MACpD,KAAK,KAAK;QAAE,OAAO,4BAA4B;MAC/C;QAAS,OAAO,0BAA0B;IAC5C;EACF,CAAC;EAED,IAAIjD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKuD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvExD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAKuD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F5D,OAAA;UAAGuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCxD,OAAA,CAACZ,OAAO;MAACyE,QAAQ,EAAC;IAAW;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhC5D,OAAA;MAAQuD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7CxD,OAAA;QAAKuD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CxD,OAAA;UAAKuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxD,OAAA;YAAKuD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxD,OAAA,CAACX,MAAM;cAACkE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C5D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAIuD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7E5D,OAAA;gBAAGuD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5D,OAAA;YAAKuD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxD,OAAA;cACE8D,OAAO,EAAE7C,kBAAmB;cAC5BsC,SAAS,EAAE,kFACThD,aAAa,GACT,gDAAgD,GAChD,0CAA0C,EAC7C;cAAAiD,QAAA,GAEFjD,aAAa,gBAAGP,OAAA,CAACL,KAAK;gBAAC4D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5D,OAAA,CAACJ,QAAQ;gBAAC2D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjF5D,OAAA;gBAAAwD,QAAA,EAAOjD,aAAa,GAAG,SAAS,GAAG;cAAU;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eAET5D,OAAA;cACE8D,OAAO,EAAErC,WAAY;cACrB8B,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHxD,OAAA,CAACT,QAAQ;gBAACgE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC5D,OAAA;gBAAAwD,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT5D,OAAA;MAAKuD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CxD,OAAA;QAAKuD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDxD,OAAA;UAAKuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxD,OAAA;YAAKuD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCxD,OAAA,CAACV,MAAM;cAACiE,SAAS,EAAC;YAA0E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/F5D,OAAA;cACE8B,IAAI,EAAC,MAAM;cACXiC,WAAW,EAAC,iBAAiB;cAC7BC,KAAK,EAAEvD,UAAW;cAClBwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CT,SAAS,EAAC;YAAoH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAKuD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChExD,OAAA;cAAAwD,QAAA,GAAK,SAAO,eAAAxD,OAAA;gBAAMuD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAE,CAAArD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0C,KAAK,CAACuB,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClG5D,OAAA;cAAAwD,QAAA,GAAK,UAAQ,eAAAxD,OAAA;gBAAMuD,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAE,CAAArD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0C,KAAK,CAACC,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAACvD,OAAO,CAAC,CAACsD,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3H5D,OAAA;cAAAwD,QAAA,GAAK,YAAU,eAAAxD,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE,CAAArD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0C,KAAK,CAACC,MAAM,CAACuB,CAAC,IAAI,CAACA,CAAC,CAACvD,OAAO,CAAC,CAACsD,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBZ,aAAa,CAACwB,MAAM,KAAK,CAAC,gBACzBpE,OAAA;UAAKuD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DxD,OAAA,CAACX,MAAM;YAACkE,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3D5D,OAAA;YAAIuD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E5D,OAAA;YAAGuD,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzB/C,UAAU,GAAG,kCAAkC,GAAG;UAAuD;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,GAENhB,aAAa,CAAC0B,GAAG,CAAEvB,IAAI,iBACrB/C,OAAA;UAAmBuD,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAChGxD,OAAA;YAAKuD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CxD,OAAA;cAAKuD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBxD,OAAA;gBAAKuD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxD,OAAA;kBAAIuD,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAET,IAAI,CAACC;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpE5D,OAAA;kBAAMuD,SAAS,EAAE,8CAA8CF,gBAAgB,CAACN,IAAI,CAACO,QAAQ,CAAC,EAAG;kBAAAE,QAAA,EAC9FT,IAAI,CAACO;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACP5D,OAAA;kBAAMuD,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EACnFT,IAAI,CAACjB;gBAAI;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN5D,OAAA;gBAAGuD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAET,IAAI,CAACI;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAExD5D,OAAA;gBAAKuD,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChExD,OAAA;kBAAAwD,QAAA,GAAM,MAAI,EAACT,IAAI,CAACK,EAAE;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACzBb,IAAI,CAACwB,OAAO,iBAAIvE,OAAA;kBAAAwD,QAAA,GAAM,WAAS,EAACT,IAAI,CAACwB,OAAO;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACpDb,IAAI,CAACyB,UAAU,IAAIzB,IAAI,CAACyB,UAAU,CAACJ,MAAM,GAAG,CAAC,iBAC5CpE,OAAA;kBAAAwD,QAAA,GAAM,UAAQ,EAACT,IAAI,CAACyB,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACjD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5D,OAAA;cAAKuD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CxD,OAAA;gBACE8D,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAAC2B,IAAI,CAACK,EAAE,EAAE,CAACL,IAAI,CAACjC,OAAO,CAAE;gBAClDyC,SAAS,EAAE,oCACTR,IAAI,CAACjC,OAAO,GACR,kCAAkC,GAClC,gCAAgC,EACnC;gBACH4D,KAAK,EAAE3B,IAAI,CAACjC,OAAO,GAAG,cAAc,GAAG,aAAc;gBAAA0C,QAAA,EAEpDT,IAAI,CAACjC,OAAO,gBAAGd,OAAA,CAACP,GAAG;kBAAC8D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5D,OAAA,CAACN,MAAM;kBAAC6D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eAET5D,OAAA;gBACE8D,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACyB,IAAI,CAACK,EAAE,CAAE;gBACnCG,SAAS,EAAC,+DAA+D;gBACzEmB,KAAK,EAAC,aAAa;gBAAAlB,QAAA,eAEnBxD,OAAA,CAACR,MAAM;kBAAC+D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA7CEb,IAAI,CAACK,EAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8CZ,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1D,EAAA,CAzOQD,GAAG;AAAA0E,EAAA,GAAH1E,GAAG;AA2OZ,eAAeA,GAAG;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}