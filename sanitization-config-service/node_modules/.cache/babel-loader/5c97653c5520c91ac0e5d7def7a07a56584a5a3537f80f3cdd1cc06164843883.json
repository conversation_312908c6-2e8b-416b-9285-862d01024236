{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.5 20H8\",\n  key: \"1k40s5\"\n}], [\"path\", {\n  d: \"M17 9h.01\",\n  key: \"1j24nn\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"16\",\n  x: \"12\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"ixliua\"\n}], [\"path\", {\n  d: \"M8 6H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h4\",\n  key: \"1mp6e1\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"15\",\n  r: \"1\",\n  key: \"tqvash\"\n}]];\nconst MonitorSpeaker = createLucideIcon(\"monitor-speaker\", __iconNode);\nexport { __iconNode, MonitorSpeaker as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "cx", "cy", "r", "MonitorSpeaker", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/monitor-speaker.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5.5 20H8', key: '1k40s5' }],\n  ['path', { d: 'M17 9h.01', key: '1j24nn' }],\n  ['rect', { width: '10', height: '16', x: '12', y: '4', rx: '2', key: 'ixliua' }],\n  ['path', { d: 'M8 6H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h4', key: '1mp6e1' }],\n  ['circle', { cx: '17', cy: '15', r: '1', key: 'tqvash' }],\n];\n\n/**\n * @component @name MonitorSpeaker\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNS41IDIwSDgiIC8+CiAgPHBhdGggZD0iTTE3IDloLjAxIiAvPgogIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxNiIgeD0iMTIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik04IDZINGEyIDIgMCAwIDAtMiAydjZhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxjaXJjbGUgY3g9IjE3IiBjeT0iMTUiIHI9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/monitor-speaker\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorSpeaker = createLucideIcon('monitor-speaker', __iconNode);\n\nexport default MonitorSpeaker;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,QAAU;EAAEM,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAU,GAC1D;AAaM,MAAAS,cAAA,GAAiBC,gBAAiB,oBAAmBZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}