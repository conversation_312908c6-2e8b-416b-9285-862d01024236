# 部署指南

## 概述

脱敏配置服务是一个纯前端应用，构建后可以部署到任何静态文件服务器上。本文档提供了多种部署方式的详细说明。

## 构建应用

在部署之前，首先需要构建生产版本：

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，所有静态文件将生成在 `build/` 目录中。

## 部署方式

### 1. Nginx 部署

#### 安装 Nginx

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置 Nginx

创建配置文件 `/etc/nginx/sites-available/sanitization-config`:

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    root /var/www/sanitization-config;
    index index.html;

    # 支持 SPA 路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

#### 部署步骤

```bash
# 复制构建文件
sudo cp -r build/* /var/www/sanitization-config/

# 启用站点
sudo ln -s /etc/nginx/sites-available/sanitization-config /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 2. Apache 部署

#### 安装 Apache

```bash
# Ubuntu/Debian
sudo apt install apache2

# CentOS/RHEL
sudo yum install httpd
```

#### 配置 Apache

创建虚拟主机配置文件：

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/sanitization-config
    
    # 支持 SPA 路由
    <Directory /var/www/sanitization-config>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # 重写规则支持 SPA
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

#### 部署步骤

```bash
# 复制构建文件
sudo cp -r build/* /var/www/sanitization-config/

# 启用模块
sudo a2enmod rewrite
sudo a2enmod expires

# 重启 Apache
sudo systemctl restart apache2
```

### 3. GitHub Pages 部署

#### 方法一：手动部署

1. 在 GitHub 上创建新仓库
2. 将 `build/` 目录内容推送到 `gh-pages` 分支
3. 在仓库设置中启用 GitHub Pages

#### 方法二：自动部署

在项目根目录创建 `.github/workflows/deploy.yml`:

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build
      run: npm run build
      
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./build
```

### 4. Netlify 部署

#### 方法一：拖拽部署

1. 访问 [Netlify](https://netlify.com)
2. 将 `build/` 目录拖拽到部署区域
3. 获得自动生成的URL

#### 方法二：Git 集成

1. 连接 GitHub 仓库
2. 设置构建命令：`npm run build`
3. 设置发布目录：`build`
4. 自动部署

#### Netlify 配置文件

创建 `netlify.toml`:

```toml
[build]
  publish = "build"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
```

### 5. Vercel 部署

#### 安装 Vercel CLI

```bash
npm i -g vercel
```

#### 部署步骤

```bash
# 登录
vercel login

# 部署
vercel --prod
```

#### Vercel 配置文件

创建 `vercel.json`:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "build"
      }
    }
  ],
  "routes": [
    {
      "src": "/static/(.*)",
      "headers": {
        "cache-control": "public, max-age=31536000, immutable"
      }
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### 6. Docker 部署

#### Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建文件
COPY --from=build /app/build /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### nginx.conf

```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

#### 构建和运行

```bash
# 构建镜像
docker build -t sanitization-config .

# 运行容器
docker run -d -p 80:80 sanitization-config
```

## 环境变量

由于是纯前端应用，所有配置都在构建时确定。如需要不同环境的配置，可以使用环境变量：

```bash
# 开发环境
REACT_APP_ENV=development npm run build

# 生产环境
REACT_APP_ENV=production npm run build
```

## 性能优化

### 1. 启用 Gzip 压缩

在服务器配置中启用 Gzip 压缩可以显著减少传输大小。

### 2. 设置缓存头

为静态资源设置长期缓存：

```nginx
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 使用 CDN

将静态资源部署到 CDN 可以提高加载速度。

## 监控和日志

### 1. 访问日志

配置服务器记录访问日志：

```nginx
access_log /var/log/nginx/sanitization-config.access.log;
error_log /var/log/nginx/sanitization-config.error.log;
```

### 2. 错误监控

可以集成前端错误监控服务如 Sentry。

## 安全考虑

### 1. HTTPS

生产环境建议使用 HTTPS：

```bash
# 使用 Let's Encrypt
sudo certbot --nginx -d your-domain.com
```

### 2. 安全头

添加安全相关的 HTTP 头：

```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

## 故障排除

### 常见问题

1. **404 错误** - 确保服务器支持 SPA 路由重写
2. **静态资源加载失败** - 检查文件路径和权限
3. **缓存问题** - 清除浏览器缓存或更新缓存策略

### 调试技巧

1. 检查服务器错误日志
2. 使用浏览器开发者工具
3. 验证文件权限和路径

## 备份和恢复

由于数据存储在用户浏览器中，建议：

1. 提供数据导出功能（已实现）
2. 定期提醒用户备份配置
3. 考虑添加云端同步功能（未来版本）
