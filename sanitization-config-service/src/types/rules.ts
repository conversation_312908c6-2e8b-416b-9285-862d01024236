export enum RuleType {
  FIELD_NAME = 'FIELD_NAME',
  PATTERN = 'PATTERN',
  CONTENT_TYPE = 'CONTENT_TYPE',
  CUSTOM = 'CUSTOM'
}

export enum Severity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum MarkerType {
  PASSWORD = 'PASSWORD',
  EMAIL = 'EMAIL',
  CREDIT_CARD = 'CREDIT_CARD',
  PHONE = 'PHONE',
  USERNAME = 'USERNAME',
  SSN = 'SSN',
  API_KEY = 'API_KEY',
  DB_PASSWORD = 'DB_PASSWORD',
  PAYMENT_DATA = 'PAYMENT_DATA',
  PII = 'PII'
}

export enum MarkerFormat {
  BRACKET = 'BRACKET',
  TAG = 'TAG',
  NONE = 'NONE'
}

export interface SanitizationRule {
  id: string;
  name: string;
  description: string;
  type: RuleType;
  severity: Severity;
  enabled: boolean;
  priority: number;
  
  // Rule matching conditions
  fieldNames?: string[];
  pattern?: string;
  contentTypes?: string[];
  
  // Sanitization configuration
  maskValue: string;
  markerType?: MarkerType;
  preserveFormat: boolean;
  preserveLength: number;
  
  // Application conditions
  includeServices?: string[];
  excludeServices?: string[];
  conditions?: Record<string, string>;
}

export interface GlobalSettings {
  defaultMaskValue: string;
  enableLogging: boolean;
  enableMetrics: boolean;
  logLevel: string;
  maxRulesPriority: number;
}

export interface RulesConfig {
  version: string;
  timestamp: number;
  enabled: boolean;
  markersEnabled: boolean;
  markerFormat: MarkerFormat;
  rules: SanitizationRule[];
  globalSettings: GlobalSettings;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  testOutput?: string;
}

export interface BatchOperationResult {
  successCount: number;
  failedCount: number;
  failedRules: string[];
}
