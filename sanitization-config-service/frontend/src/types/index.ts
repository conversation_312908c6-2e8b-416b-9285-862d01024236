export type RuleType = 'FIELD_NAME' | 'PATTERN' | 'CONTENT_TYPE' | 'CUSTOM';

export type SeverityLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export interface SanitizationRule {
  id: string;
  name: string;
  description: string;
  type: RuleType;
  severity: SeverityLevel;
  enabled: boolean;
  priority: number;
  
  // Rule matching conditions
  fieldNames?: string[];
  pattern?: string;
  contentTypes?: string[];
  
  // Sanitization configuration
  maskValue: string;
  markerType?: string;
  preserveFormat: boolean;
  preserveLength: number;
  
  // Application conditions
  includeServices?: string[];
  excludeServices?: string[];
  conditions?: Record<string, string>;
}

export interface SanitizationConfig {
  version: string;
  timestamp: number;
  enabled: boolean;
  markersEnabled: boolean;
  markerFormat: string;
  rules: SanitizationRule[];
  globalSettings?: Record<string, any>;
}

export interface HealthResponse {
  status: string;
  timestamp: number;
  version: string;
  ruleCount: number;
}

export interface MetricsResponse {
  totalRules: number;
  enabledRules: number;
  disabledRules: number;
  rulesByType: Record<string, number>;
  rulesBySeverity: Record<string, number>;
  configVersion: string;
  lastUpdated: number;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  timestamp?: number;
}

export interface BatchOperationResponse {
  successCount: number;
  failedCount: number;
  failedRules?: string[];
  message: string;
  timestamp: number;
}

export interface ValidationResponse {
  valid: boolean;
  errors?: string[];
  testOutput?: string;
  message: string;
  timestamp: number;
}
