#!/bin/bash

# Start development environment for Sanitization Config Service

echo "🚀 启动脱敏规则管理系统 (开发模式)"
echo "======================================="

# Function to cleanup processes on exit
cleanup() {
    echo ""
    echo "🛑 正在关闭服务..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "   ✅ 后端服务已停止"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "   ✅ 前端服务已停止"
    fi
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start backend service
echo "🔧 启动后端服务..."
cd "$(dirname "$0")"
SERVER_PORT=8081 go run main.go &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend is running
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ 后端服务已启动: http://localhost:8081"
else
    echo "⚠️  后端服务可能启动失败，请检查日志"
fi

# Start frontend development server
echo "🎨 启动前端服务..."
cd frontend
npm start &
FRONTEND_PID=$!

echo ""
echo "🌟 服务状态:"
echo "   后端API:  http://localhost:8081"
echo "   前端界面: http://localhost:3000"
echo "   健康检查: http://localhost:8081/health"
echo ""
echo "📋 主要功能:"
echo "   ✨ 脱敏规则管理 - 创建、编辑、删除规则"
echo "   🔄 批量操作 - 批量启用、禁用、删除"
echo "   📤 导入导出 - 配置文件导入导出"
echo "   🔧 全局开关 - 一键启用/禁用脱敏"
echo ""
echo "💡 使用提示:"
echo "   - 按 Ctrl+C 停止所有服务"
echo "   - 前端代码修改会自动重载"
echo "   - 后端代码修改需要手动重启"
echo ""
echo "🎯 开发环境就绪! 按 Ctrl+C 停止服务。"

# Wait for processes
wait $BACKEND_PID $FRONTEND_PID
