import { FastifyBaseLogger } from 'fastify';
import { config } from '../config';

export interface Logger {
  trace(msg: string, ...args: unknown[]): void;
  debug(msg: string, ...args: unknown[]): void;
  info(msg: string, ...args: unknown[]): void;
  warn(msg: string, ...args: unknown[]): void;
  error(msg: string, ...args: unknown[]): void;
  fatal(msg: string, ...args: unknown[]): void;
  child(bindings: Record<string, unknown>): Logger;
}

/**
 * Create logger configuration based on app config
 */
export function createLoggerOptions() {
  return {
    level: config.logging.level,
    ...(config.logging.format === 'json' ? {} : {
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      }
    })
  };
}

/**
 * Create a logger instance for non-Fastify contexts
 */
export function createLogger(name: string): Logger {
  const pino = require('pino');
  const logger = pino({
    name,
    ...createLoggerOptions()
  });
  
  return logger as Logger;
}
