{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-non-null-assertion": "error", "no-console": ["warn", {"allow": ["warn", "error"]}]}, "env": {"node": true, "es2022": true}, "ignorePatterns": ["dist", "node_modules", "*.js", "vitest.config.ts"]}