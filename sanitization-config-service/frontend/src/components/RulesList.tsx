import React, { useState } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Power,
  PowerOff,
  Filter,
  MoreVertical,
  CheckSquare,
  Square,
  Download,
  Upload
} from 'lucide-react';
import { SanitizationRule, SanitizationConfig } from '../types';
import { sanitizationApi } from '../services/api';
import toast from 'react-hot-toast';

interface RulesListProps {
  config: SanitizationConfig | null;
  onRefresh: () => void;
  onEditRule: (rule: SanitizationRule) => void;
  onCreateRule: () => void;
}

const RulesList: React.FC<RulesListProps> = ({ config, onRefresh, onEditRule, onCreateRule }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  const filteredRules = config?.rules.filter(rule => {
    const matchesSearch = 
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || rule.type === filterType;
    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;
    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'enabled' && rule.enabled) ||
      (filterStatus === 'disabled' && !rule.enabled);

    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  }) || [];

  const handleSelectRule = (ruleId: string) => {
    const newSelected = new Set(selectedRules);
    if (newSelected.has(ruleId)) {
      newSelected.delete(ruleId);
    } else {
      newSelected.add(ruleId);
    }
    setSelectedRules(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRules.size === filteredRules.length) {
      setSelectedRules(new Set());
    } else {
      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));
    }
  };

  const handleBatchOperation = async (operation: 'enable' | 'disable' | 'delete') => {
    if (selectedRules.size === 0) {
      toast.error('Please select rules first');
      return;
    }

    const confirmMessage = `Are you sure you want to ${operation} ${selectedRules.size} rule(s)?`;
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const result = await sanitizationApi.batchOperation(Array.from(selectedRules), operation);
      toast.success(`${operation} operation completed: ${result.successCount} succeeded, ${result.failedCount} failed`);
      setSelectedRules(new Set());
      onRefresh();
    } catch (error) {
      toast.error(`Batch ${operation} failed`);
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await sanitizationApi.toggleRule(ruleId, enabled);
      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);
      onRefresh();
    } catch (error) {
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }

    try {
      await sanitizationApi.deleteRule(ruleId);
      toast.success('Rule deleted successfully');
      onRefresh();
    } catch (error) {
      toast.error('Failed to delete rule');
    }
  };

  const handleExportRules = async () => {
    try {
      const config = await sanitizationApi.exportRules();
      const dataStr = JSON.stringify(config, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `sanitization-rules-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Rules exported successfully');
    } catch (error) {
      toast.error('Failed to export rules');
    }
  };

  const handleImportRules = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        await sanitizationApi.importRules(config);
        toast.success('Rules imported successfully');
        onRefresh();
      } catch (error) {
        toast.error('Failed to import rules');
      }
    };
    reader.readAsText(file);
    // Reset the input
    event.target.value = '';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-gradient-to-r from-red-100 to-red-200 text-red-700 border-0';
      case 'HIGH': return 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700 border-0';
      case 'MEDIUM': return 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-700 border-0';
      case 'LOW': return 'bg-gradient-to-r from-green-100 to-green-200 text-green-700 border-0';
      default: return 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border-0';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 border-0';
      case 'PATTERN': return 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-700 border-0';
      case 'CONTENT_TYPE': return 'bg-gradient-to-r from-indigo-100 to-indigo-200 text-indigo-700 border-0';
      default: return 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border-0';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl border-0 p-8 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              脱敏规则管理
            </h2>
            <p className="text-gray-500 text-base mt-2 font-medium">
              共 {config?.rules.length || 0} 条规则，显示 {filteredRules.length} 条
              {selectedRules.size > 0 && (
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                  已选择 {selectedRules.size} 条
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleExportRules}
              className="inline-flex items-center px-5 py-3 bg-white border-0 rounded-xl text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <Download className="h-4 w-4 mr-2" />
              导出配置
            </button>
            <label className="inline-flex items-center px-5 py-3 bg-white border-0 rounded-xl text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              导入配置
              <input
                type="file"
                accept=".json"
                onChange={handleImportRules}
                className="hidden"
              />
            </label>
            <button
              onClick={onCreateRule}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl text-sm font-semibold transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <Plus className="h-4 w-4 mr-2" />
              新建规则
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl border-0 p-6 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索规则名称、描述或ID..."
              className="w-full pl-12 pr-4 py-3.5 border-0 rounded-xl text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-5 py-3.5 rounded-xl text-sm font-medium transition-all duration-200 ${
              showFilters
                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm'
                : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 pt-6 border-t border-gray-100">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">规则类型</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              >
                <option value="all">全部类型</option>
                <option value="FIELD_NAME">字段名匹配</option>
                <option value="PATTERN">正则匹配</option>
                <option value="CONTENT_TYPE">内容类型</option>
                <option value="CUSTOM">自定义</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">严重程度</label>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              >
                <option value="all">全部级别</option>
                <option value="CRITICAL">关键</option>
                <option value="HIGH">高</option>
                <option value="MEDIUM">中等</option>
                <option value="LOW">低</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">启用状态</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              >
                <option value="all">全部状态</option>
                <option value="enabled">已启用</option>
                <option value="disabled">已禁用</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Batch Actions */}
      {selectedRules.size > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border-0 rounded-2xl p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <CheckSquare className="h-4 w-4 text-white" />
              </div>
              <span className="text-gray-800 font-semibold text-lg">
                已选择 {selectedRules.size} 条规则
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleBatchOperation('enable')}
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Power className="h-4 w-4 mr-2" />
                批量启用
              </button>
              <button
                onClick={() => handleBatchOperation('disable')}
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <PowerOff className="h-4 w-4 mr-2" />
                批量禁用
              </button>
              <button
                onClick={() => handleBatchOperation('delete')}
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                批量删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="space-y-4">
        {filteredRules.length === 0 ? (
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl border-0 p-20 text-center shadow-sm">
            <div className="w-20 h-20 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">未找到规则</h3>
            <p className="text-base text-gray-500 max-w-md mx-auto">
              {searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all'
                ? '请尝试调整搜索条件或筛选器'
                : '创建您的第一个脱敏规则开始使用'
              }
            </p>
          </div>
        ) : (
          <>
            {/* Select All */}
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl border-0 p-5 shadow-sm">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleSelectAll}
                  className="text-gray-500 hover:text-blue-600 transition-all duration-200 p-1 rounded-lg hover:bg-blue-50"
                >
                  {selectedRules.size === filteredRules.length ? (
                    <CheckSquare className="h-5 w-5" />
                  ) : (
                    <Square className="h-5 w-5" />
                  )}
                </button>
                <span className="text-gray-700 font-medium">
                  全选 {filteredRules.length} 条规则
                </span>
              </div>
            </div>

            {/* Rules */}
            {filteredRules.map((rule) => (
              <div
                key={rule.id}
                className={`bg-white/70 backdrop-blur-sm border-0 rounded-2xl p-8 transition-all duration-300 hover:shadow-lg hover:bg-white/90 ${
                  selectedRules.has(rule.id)
                    ? 'ring-2 ring-blue-500 bg-gradient-to-r from-blue-50/80 to-purple-50/80'
                    : 'shadow-sm hover:shadow-md'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-5 flex-1">
                    <button
                      onClick={() => handleSelectRule(rule.id)}
                      className="mt-2 text-gray-400 hover:text-blue-600 transition-all duration-200 p-1 rounded-lg hover:bg-blue-50"
                    >
                      {selectedRules.has(rule.id) ? (
                        <CheckSquare className="h-5 w-5" />
                      ) : (
                        <Square className="h-5 w-5" />
                      )}
                    </button>

                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h4 className="text-xl font-bold text-gray-900">{rule.name}</h4>
                        <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold ${getSeverityColor(rule.severity)}`}>
                          {rule.severity === 'CRITICAL' ? '关键' :
                           rule.severity === 'HIGH' ? '高' :
                           rule.severity === 'MEDIUM' ? '中等' : '低'}
                        </span>
                        <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold ${getTypeColor(rule.type)}`}>
                          {rule.type === 'FIELD_NAME' ? '字段匹配' :
                           rule.type === 'PATTERN' ? '正则匹配' :
                           rule.type === 'CONTENT_TYPE' ? '内容类型' : '自定义'}
                        </span>
                      </div>
                      <p className="text-base text-gray-600 mb-3 leading-relaxed">{rule.description}</p>
                      <div className="text-sm text-gray-500 mb-4 font-medium">规则ID: {rule.id}</div>
                      
                      <div className="space-y-3">
                        {rule.fieldNames && rule.fieldNames.length > 0 && (
                          <div className="flex items-center space-x-3">
                            <span className="font-semibold text-gray-700 text-sm">匹配字段:</span>
                            <div className="flex flex-wrap gap-2">
                              {rule.fieldNames.map((field, index) => (
                                <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium">
                                  {field}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                        {rule.pattern && (
                          <div className="flex items-center space-x-3">
                            <span className="font-semibold text-gray-700 text-sm">正则表达式:</span>
                            <code className="px-3 py-2 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-lg text-sm font-mono border-0">
                              {rule.pattern}
                            </code>
                          </div>
                        )}
                        <div className="flex items-center space-x-3">
                          <span className="font-semibold text-gray-700 text-sm">脱敏值:</span>
                          <code className="px-3 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-lg text-sm font-mono border-0">
                            {rule.maskValue}
                          </code>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className={`flex items-center px-4 py-2 rounded-full ${
                      rule.enabled
                        ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700'
                        : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600'
                    }`}>
                      <div className={`w-2 h-2 rounded-full mr-3 ${
                        rule.enabled ? 'bg-green-500' : 'bg-gray-400'
                      }`}></div>
                      <span className="text-sm font-semibold">
                        {rule.enabled ? '已启用' : '已禁用'}
                      </span>
                    </div>

                    <div className="relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id)}
                        className="p-3 hover:bg-gray-100 rounded-xl transition-all duration-200"
                      >
                        <MoreVertical className="h-5 w-5 text-gray-500" />
                      </button>

                      {actionMenuOpen === rule.id && (
                        <div className="absolute right-0 mt-2 w-36 bg-white/95 backdrop-blur-sm border-0 rounded-xl shadow-xl z-10">
                          <button
                            onClick={() => {
                              onEditRule(rule);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-3 text-left text-gray-700 hover:bg-blue-50 flex items-center space-x-3 text-sm font-medium rounded-t-xl transition-colors"
                          >
                            <Edit className="h-4 w-4" />
                            <span>编辑</span>
                          </button>
                          <button
                            onClick={() => {
                              handleToggleRule(rule.id, !rule.enabled);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-3 text-left text-gray-700 hover:bg-blue-50 flex items-center space-x-3 text-sm font-medium transition-colors"
                          >
                            {rule.enabled ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
                            <span>{rule.enabled ? '禁用' : '启用'}</span>
                          </button>
                          <button
                            onClick={() => {
                              handleDeleteRule(rule.id);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-3 text-left text-red-600 hover:bg-red-50 flex items-center space-x-3 text-sm font-medium rounded-b-xl transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>删除</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default RulesList;
