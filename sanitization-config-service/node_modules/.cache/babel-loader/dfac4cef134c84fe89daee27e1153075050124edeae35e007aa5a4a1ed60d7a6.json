{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22v-6.57\",\n  key: \"1wmca3\"\n}], [\"path\", {\n  d: \"M12 11h.01\",\n  key: \"z322tv\"\n}], [\"path\", {\n  d: \"M12 7h.01\",\n  key: \"1ivr5q\"\n}], [\"path\", {\n  d: \"M14 15.43V22\",\n  key: \"1q2vjd\"\n}], [\"path\", {\n  d: \"M15 16a5 5 0 0 0-6 0\",\n  key: \"o9wqvi\"\n}], [\"path\", {\n  d: \"M16 11h.01\",\n  key: \"xkw8gn\"\n}], [\"path\", {\n  d: \"M16 7h.01\",\n  key: \"1kdx03\"\n}], [\"path\", {\n  d: \"M8 11h.01\",\n  key: \"1dfujw\"\n}], [\"path\", {\n  d: \"M8 7h.01\",\n  key: \"1vti4s\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"2\",\n  width: \"16\",\n  height: \"20\",\n  rx: \"2\",\n  key: \"1uxh74\"\n}]];\nconst Hotel = createLucideIcon(\"hotel\", __iconNode);\nexport { __iconNode, Hotel as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "Hotel", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/hotel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 22v-6.57', key: '1wmca3' }],\n  ['path', { d: 'M12 11h.01', key: 'z322tv' }],\n  ['path', { d: 'M12 7h.01', key: '1ivr5q' }],\n  ['path', { d: 'M14 15.43V22', key: '1q2vjd' }],\n  ['path', { d: 'M15 16a5 5 0 0 0-6 0', key: 'o9wqvi' }],\n  ['path', { d: 'M16 11h.01', key: 'xkw8gn' }],\n  ['path', { d: 'M16 7h.01', key: '1kdx03' }],\n  ['path', { d: 'M8 11h.01', key: '1dfujw' }],\n  ['path', { d: 'M8 7h.01', key: '1vti4s' }],\n  ['rect', { x: '4', y: '2', width: '16', height: '20', rx: '2', key: '1uxh74' }],\n];\n\n/**\n * @component @name Hotel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjJ2LTYuNTciIC8+CiAgPHBhdGggZD0iTTEyIDExaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgN2guMDEiIC8+CiAgPHBhdGggZD0iTTE0IDE1LjQzVjIyIiAvPgogIDxwYXRoIGQ9Ik0xNSAxNmE1IDUgMCAwIDAtNiAwIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMWguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDdoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDExaC4wMSIgLz4KICA8cGF0aCBkPSJNOCA3aC4wMSIgLz4KICA8cmVjdCB4PSI0IiB5PSIyIiB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/hotel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Hotel = createLucideIcon('hotel', __iconNode);\n\nexport default Hotel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAM,KAAA,GAAQC,gBAAiB,UAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}