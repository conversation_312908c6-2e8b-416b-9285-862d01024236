# Sanitization Config Service - Node.js Backend

A Node.js/TypeScript backend implementation using Fastify framework for the sanitization configuration service.

## Features

- **Fastify Framework**: High-performance web framework
- **TypeScript**: Type-safe development
- **Plugin Architecture**: Modular design with routes, services, middleware, and utils
- **JWT Authentication**: Optional JWT-based authentication
- **Rate Limiting**: Built-in rate limiting support
- **Metrics**: Application metrics collection
- **Environment Configuration**: Mirrors Go backend environment variables
- **Unit Testing**: Vitest testing framework

## Project Structure

```
backend/
├── src/
│   ├── config/         # Configuration management
│   ├── middleware/     # Fastify middleware (auth, rate-limit, etc.)
│   ├── routes/         # API route handlers
│   ├── services/       # Business logic services
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── models/         # Data models
│   └── index.ts        # Application entry point
├── test/               # Test files
├── dist/               # Compiled JavaScript output
└── package.json        # Project dependencies
```

## Environment Variables

The following environment variables are supported (mirroring the Go backend):

```bash
# Server Configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s

# Authentication Configuration
AUTH_ENABLED=false
JWT_SECRET=your-secret-key
TOKEN_HEADER=Authorization

# Rules Configuration
RULES_CONFIG_FILE=config/rules.json
RULES_RELOAD_ENABLED=true
RULES_RELOAD_INTERVAL=5m

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
```

## Development

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
npm install
```

### Running in Development

```bash
npm run dev
```

This uses `ts-node-dev` for automatic reloading on file changes.

### Building for Production

```bash
npm run build
npm start
```

### Testing

```bash
# Run tests
npm test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### Linting

```bash
# Check for linting errors
npm run lint

# Auto-fix linting errors
npm run lint:fix
```

### Type Checking

```bash
npm run typecheck
```

## API Documentation

The API endpoints mirror the Go backend implementation. Key endpoints include:

- `GET /health` - Health check endpoint
- `GET /api/rules` - Get sanitization rules
- `POST /api/rules` - Update sanitization rules (requires auth if enabled)
- `GET /api/validate` - Validate rules configuration

## Plugin Architecture

The application follows a plugin-based architecture:

- **Routes**: Define API endpoints and request handlers
- **Services**: Contain business logic and data processing
- **Middleware**: Handle cross-cutting concerns (auth, logging, rate-limiting)
- **Utils**: Provide helper functions and utilities

## Type Definitions

The backend includes full TypeScript type definitions for:

- `SanitizationRule`: Matches the rules.json schema
- `Config`: Application configuration types
- Request/Response types for all API endpoints

## Adding New Features

1. **New Route**: Create a file in `src/routes/`
2. **New Service**: Create a file in `src/services/`
3. **New Middleware**: Create a file in `src/middleware/`
4. **New Types**: Add to existing files in `src/types/` or create new ones

## Performance Considerations

- Fastify is one of the fastest Node.js web frameworks
- JSON schema validation is built-in for request/response validation
- Supports async/await patterns throughout
- Connection pooling and proper resource management

## Security

- JWT authentication when enabled
- Rate limiting to prevent abuse
- Input validation using JSON schemas
- Environment variable separation for secrets
