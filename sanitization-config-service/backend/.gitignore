# Dependencies
node_modules/

# Build output
dist/

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Test coverage
coverage/
.nyc_output/

# TypeScript cache
*.tsbuildinfo

# Package manager lock files (keep one, ignore others)
# package-lock.json
yarn.lock
pnpm-lock.yaml
