{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.531 12.469 6.619 20.38a1 1 0 1 1-3-3l7.912-7.912\",\n  key: \"we99rg\"\n}], [\"path\", {\n  d: \"M15.686 4.314A12.5 12.5 0 0 0 5.461 2.958 1 1 0 0 0 5.58 4.71a22 22 0 0 1 6.318 3.393\",\n  key: \"1w6hck\"\n}], [\"path\", {\n  d: \"M17.7 3.7a1 1 0 0 0-1.4 0l-4.6 4.6a1 1 0 0 0 0 1.4l2.6 2.6a1 1 0 0 0 1.4 0l4.6-4.6a1 1 0 0 0 0-1.4z\",\n  key: \"15hgfx\"\n}], [\"path\", {\n  d: \"M19.686 8.314a12.501 12.501 0 0 1 1.356 10.225 1 1 0 0 1-1.751-.119 22 22 0 0 0-3.393-6.319\",\n  key: \"452b4h\"\n}]];\nconst Pickaxe = createLucideIcon(\"pickaxe\", __iconNode);\nexport { __iconNode, Pickaxe as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Pickaxe", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/pickaxe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14.531 12.469 6.619 20.38a1 1 0 1 1-3-3l7.912-7.912', key: 'we99rg' }],\n  [\n    'path',\n    {\n      d: 'M15.686 4.314A12.5 12.5 0 0 0 5.461 2.958 1 1 0 0 0 5.58 4.71a22 22 0 0 1 6.318 3.393',\n      key: '1w6hck',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M17.7 3.7a1 1 0 0 0-1.4 0l-4.6 4.6a1 1 0 0 0 0 1.4l2.6 2.6a1 1 0 0 0 1.4 0l4.6-4.6a1 1 0 0 0 0-1.4z',\n      key: '15hgfx',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M19.686 8.314a12.501 12.501 0 0 1 1.356 10.225 1 1 0 0 1-1.751-.119 22 22 0 0 0-3.393-6.319',\n      key: '452b4h',\n    },\n  ],\n];\n\n/**\n * @component @name Pickaxe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTMxIDEyLjQ2OSA2LjYxOSAyMC4zOGExIDEgMCAxIDEtMy0zbDcuOTEyLTcuOTEyIiAvPgogIDxwYXRoIGQ9Ik0xNS42ODYgNC4zMTRBMTIuNSAxMi41IDAgMCAwIDUuNDYxIDIuOTU4IDEgMSAwIDAgMCA1LjU4IDQuNzFhMjIgMjIgMCAwIDEgNi4zMTggMy4zOTMiIC8+CiAgPHBhdGggZD0iTTE3LjcgMy43YTEgMSAwIDAgMC0xLjQgMGwtNC42IDQuNmExIDEgMCAwIDAgMCAxLjRsMi42IDIuNmExIDEgMCAwIDAgMS40IDBsNC42LTQuNmExIDEgMCAwIDAgMC0xLjR6IiAvPgogIDxwYXRoIGQ9Ik0xOS42ODYgOC4zMTRhMTIuNTAxIDEyLjUwMSAwIDAgMSAxLjM1NiAxMC4yMjUgMSAxIDAgMCAxLTEuNzUxLS4xMTkgMjIgMjIgMCAwIDAtMy4zOTMtNi4zMTkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pickaxe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pickaxe = createLucideIcon('pickaxe', __iconNode);\n\nexport default Pickaxe;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,sDAAwD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrF,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}