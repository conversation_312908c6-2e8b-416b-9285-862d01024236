import { describe, it, expect, beforeEach, vi } from 'vitest';
import { loadConfig } from './index';

describe('Config', () => {
  beforeEach(() => {
    // Reset environment variables
    vi.resetModules();
    delete process.env.SERVER_PORT;
    delete process.env.SERVER_HOST;
    delete process.env.AUTH_ENABLED;
    delete process.env.JWT_SECRET;
  });

  describe('loadConfig', () => {
    it('should load default configuration when no env vars are set', () => {
      const config = loadConfig();
      
      expect(config.server.port).toBe(8080);
      expect(config.server.host).toBe('0.0.0.0');
      expect(config.auth.enabled).toBe(false);
      expect(config.auth.jwtSecret).toBe('your-secret-key');
      expect(config.rules.configFile).toBe('config/rules.json');
      expect(config.logging.level).toBe('info');
      expect(config.logging.format).toBe('json');
    });

    it('should load configuration from environment variables', () => {
      process.env.SERVER_PORT = '3000';
      process.env.SERVER_HOST = 'localhost';
      process.env.AUTH_ENABLED = 'true';
      process.env.JWT_SECRET = 'test-secret';
      process.env.LOG_LEVEL = 'debug';
      
      const config = loadConfig();
      
      expect(config.server.port).toBe(3000);
      expect(config.server.host).toBe('localhost');
      expect(config.auth.enabled).toBe(true);
      expect(config.auth.jwtSecret).toBe('test-secret');
      expect(config.logging.level).toBe('debug');
    });

    it('should parse duration strings correctly', () => {
      process.env.SERVER_READ_TIMEOUT = '45s';
      process.env.SERVER_WRITE_TIMEOUT = '2m';
      process.env.RULES_RELOAD_INTERVAL = '1h';
      
      const config = loadConfig();
      
      expect(config.server.readTimeout).toBe(45000); // 45 seconds in ms
      expect(config.server.writeTimeout).toBe(120000); // 2 minutes in ms
      expect(config.rules.reloadInterval).toBe(3600000); // 1 hour in ms
    });

    it('should handle invalid values gracefully', () => {
      process.env.SERVER_PORT = 'invalid';
      process.env.AUTH_ENABLED = 'invalid';
      process.env.SERVER_READ_TIMEOUT = 'invalid';
      
      const config = loadConfig();
      
      expect(config.server.port).toBe(8080); // Falls back to default
      expect(config.auth.enabled).toBe(false); // Falls back to default
      expect(config.server.readTimeout).toBe(30000); // Falls back to default
    });
  });
});
