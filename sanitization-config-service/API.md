# API 文档

## 概述

脱敏配置服务提供了一套完整的API接口，用于管理脱敏规则。虽然当前版本是纯前端实现（使用LocalStorage），但API设计遵循RESTful规范，便于未来扩展为真实的后端服务。

## 基础信息

- **基础路径**: 无（本地存储实现）
- **数据格式**: JSON
- **认证方式**: 无（本地应用）
- **版本**: v1.0.0

## 数据类型

### SanitizationRule

脱敏规则对象：

```typescript
interface SanitizationRule {
  id: string;                    // 规则唯一标识
  name: string;                  // 规则名称
  description: string;           // 规则描述
  type: RuleType;               // 规则类型
  severity: SeverityLevel;       // 严重程度
  enabled: boolean;             // 是否启用
  priority?: number;            // 优先级（可选）
  fieldNames?: string[];        // 匹配字段名（可选）
  pattern?: string;             // 正则表达式（可选）
  contentTypes?: string[];      // 内容类型（可选）
  maskValue: string;            // 脱敏值
  markerType?: string;          // 标记类型（可选）
  preserveFormat?: boolean;     // 保持格式（可选）
  preserveLength?: number;      // 保持长度（可选）
  includeServices?: string[];   // 包含服务（可选）
  excludeServices?: string[];   // 排除服务（可选）
  conditions?: Record<string, string>; // 条件（可选）
  createdAt?: string;           // 创建时间
  updatedAt?: string;           // 更新时间
}
```

### RuleType

规则类型枚举：

```typescript
type RuleType = 'FIELD_NAME' | 'PATTERN' | 'CONTENT_TYPE' | 'CUSTOM';
```

### SeverityLevel

严重程度枚举：

```typescript
type SeverityLevel = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
```

### SanitizationConfig

配置对象：

```typescript
interface SanitizationConfig {
  version?: string;             // 配置版本
  timestamp?: number;           // 时间戳
  enabled: boolean;             // 全局启用状态
  markersEnabled?: boolean;     // 标记启用状态
  markerFormat?: string;        // 标记格式
  rules: SanitizationRule[];    // 规则列表
  globalSettings?: Record<string, any>; // 全局设置
}
```

## API 接口

### 1. 获取规则列表

获取所有脱敏规则。

**方法**: `getRules(serviceName?: string)`

**参数**:
- `serviceName` (可选): 服务名称，用于过滤特定服务的规则

**返回值**: `Promise<SanitizationConfig>`

**示例**:
```typescript
const config = await sanitizationApi.getRules();
console.log(config.rules); // 所有规则
```

### 2. 创建规则

创建新的脱敏规则。

**方法**: `createRule(rule: SanitizationRule)`

**参数**:
- `rule`: 规则对象（不需要包含id、createdAt、updatedAt）

**返回值**: `Promise<ApiResponse<SanitizationRule>>`

**示例**:
```typescript
const newRule = {
  name: '信用卡脱敏',
  description: '对信用卡号进行脱敏处理',
  type: 'PATTERN',
  pattern: '\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}',
  maskValue: '****-****-****-****',
  severity: 'CRITICAL',
  enabled: true
};

const response = await sanitizationApi.createRule(newRule);
if (response.success) {
  console.log('规则创建成功:', response.data);
}
```

### 3. 更新规则

更新现有的脱敏规则。

**方法**: `updateRule(ruleId: string, rule: SanitizationRule)`

**参数**:
- `ruleId`: 规则ID
- `rule`: 更新后的规则对象

**返回值**: `Promise<ApiResponse<SanitizationRule>>`

**示例**:
```typescript
const updatedRule = {
  ...existingRule,
  name: '更新后的规则名称',
  enabled: false
};

const response = await sanitizationApi.updateRule('rule-id', updatedRule);
```

### 4. 删除规则

删除指定的脱敏规则。

**方法**: `deleteRule(ruleId: string)`

**参数**:
- `ruleId`: 规则ID

**返回值**: `Promise<ApiResponse<any>>`

**示例**:
```typescript
const response = await sanitizationApi.deleteRule('rule-id');
if (response.success) {
  console.log('规则删除成功');
}
```

### 5. 切换规则状态

启用或禁用指定规则。

**方法**: `toggleRule(ruleId: string, enabled: boolean)`

**参数**:
- `ruleId`: 规则ID
- `enabled`: 启用状态

**返回值**: `Promise<ApiResponse<SanitizationRule>>`

**示例**:
```typescript
// 禁用规则
const response = await sanitizationApi.toggleRule('rule-id', false);
```

### 6. 批量操作

对多个规则执行批量操作。

**方法**: `batchOperation(ruleIds: string[], operation: 'enable' | 'disable' | 'delete')`

**参数**:
- `ruleIds`: 规则ID数组
- `operation`: 操作类型

**返回值**: `Promise<BatchOperationResponse>`

**示例**:
```typescript
const ruleIds = ['rule-1', 'rule-2', 'rule-3'];
const response = await sanitizationApi.batchOperation(ruleIds, 'enable');
console.log(`成功: ${response.successCount}, 失败: ${response.failureCount}`);
```

### 7. 验证规则

验证规则配置的正确性。

**方法**: `validateRule(rule: SanitizationRule, testInput?: string)`

**参数**:
- `rule`: 要验证的规则
- `testInput` (可选): 测试输入数据

**返回值**: `Promise<ValidationResponse>`

**示例**:
```typescript
const rule = {
  name: '测试规则',
  type: 'PATTERN',
  pattern: '\\d{11}',
  maskValue: '***-****-****',
  // ... 其他属性
};

const response = await sanitizationApi.validateRule(rule, '13812345678');
if (response.valid) {
  console.log('规则验证通过');
  if (response.testResult) {
    console.log('脱敏结果:', response.testResult.masked);
  }
} else {
  console.log('验证错误:', response.errors);
}
```

### 8. 重载规则

重载规则配置（重置为默认配置）。

**方法**: `reloadRules()`

**返回值**: `Promise<ApiResponse<any>>`

**示例**:
```typescript
const response = await sanitizationApi.reloadRules();
```

### 9. 全局开关

切换全局脱敏开关。

**方法**: `toggleGlobalSwitch(enabled: boolean)`

**参数**:
- `enabled`: 全局启用状态

**返回值**: `Promise<{enabled: boolean; message: string; timestamp: number}>`

**示例**:
```typescript
const response = await sanitizationApi.toggleGlobalSwitch(true);
console.log(response.message); // "Global sanitization enabled"
```

### 10. 健康检查

获取服务健康状态。

**方法**: `getHealth()`

**返回值**: `Promise<HealthResponse>`

**示例**:
```typescript
const health = await sanitizationApi.getHealth();
console.log('服务状态:', health.status);
```

### 11. 获取指标

获取服务使用指标。

**方法**: `getMetrics()`

**返回值**: `Promise<MetricsResponse>`

**示例**:
```typescript
const metrics = await sanitizationApi.getMetrics();
console.log('总规则数:', metrics.totalRules);
console.log('启用规则数:', metrics.enabledRules);
```

### 12. 导出配置

导出所有规则配置。

**方法**: `exportRules()`

**返回值**: `Promise<SanitizationConfig>`

**示例**:
```typescript
const config = await sanitizationApi.exportRules();
const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
// 创建下载链接...
```

### 13. 导入配置

导入规则配置。

**方法**: `importRules(config: SanitizationConfig)`

**参数**:
- `config`: 配置对象

**返回值**: `Promise<ApiResponse<any>>`

**示例**:
```typescript
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const file = fileInput.files[0];
const text = await file.text();
const config = JSON.parse(text);

const response = await sanitizationApi.importRules(config);
if (response.success) {
  console.log('导入成功:', response.message);
}
```

## 响应格式

### ApiResponse

标准API响应格式：

```typescript
interface ApiResponse<T> {
  success: boolean;      // 操作是否成功
  data?: T;             // 响应数据
  error?: string;       // 错误信息
  message?: string;     // 提示信息
  timestamp?: number;   // 时间戳
}
```

### BatchOperationResponse

批量操作响应格式：

```typescript
interface BatchOperationResponse {
  success: boolean;      // 整体操作是否成功
  successCount: number;  // 成功数量
  failureCount: number;  // 失败数量
  failedRules?: string[]; // 失败的规则ID
  message: string;       // 操作结果信息
  timestamp?: number;    // 时间戳
}
```

### ValidationResponse

验证响应格式：

```typescript
interface ValidationResponse {
  valid: boolean;        // 验证是否通过
  errors?: string[];     // 错误信息列表
  message?: string;      // 验证信息
  timestamp?: number;    // 时间戳
  testResult?: {         // 测试结果
    input: string;       // 输入数据
    matches: string[];   // 匹配结果
    masked: string;      // 脱敏结果
  } | null;
  testOutput?: string;   // 向后兼容的测试输出
}
```

## 错误处理

所有API方法都会抛出异常或返回错误信息。建议使用try-catch处理：

```typescript
try {
  const response = await sanitizationApi.createRule(newRule);
  if (response.success) {
    // 处理成功情况
  } else {
    // 处理业务错误
    console.error('操作失败:', response.error);
  }
} catch (error) {
  // 处理系统错误
  console.error('系统错误:', error);
}
```

## 使用示例

### 完整的规则管理流程

```typescript
// 1. 获取现有规则
const config = await sanitizationApi.getRules();
console.log('现有规则数量:', config.rules.length);

// 2. 创建新规则
const newRule = {
  name: '银行卡号脱敏',
  description: '对银行卡号进行脱敏处理',
  type: 'PATTERN',
  pattern: '\\d{16,19}',
  maskValue: '****-****-****-****',
  severity: 'CRITICAL',
  enabled: true
};

const createResponse = await sanitizationApi.createRule(newRule);
const ruleId = createResponse.data?.id;

// 3. 验证规则
const validationResponse = await sanitizationApi.validateRule(
  createResponse.data!,
  '6222021234567890123'
);

if (validationResponse.valid) {
  console.log('脱敏结果:', validationResponse.testResult?.masked);
}

// 4. 更新规则
const updatedRule = {
  ...createResponse.data!,
  description: '更新后的描述'
};
await sanitizationApi.updateRule(ruleId!, updatedRule);

// 5. 导出配置
const exportConfig = await sanitizationApi.exportRules();
console.log('导出的规则数量:', exportConfig.rules.length);
```
