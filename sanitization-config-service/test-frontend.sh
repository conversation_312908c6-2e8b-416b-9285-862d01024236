#!/bin/bash

# Test script for Sanitization Config Service Frontend

echo "🧪 Testing Sanitization Config Service Frontend"
echo "=============================================="

# Check if backend is running
echo "1. Checking backend service..."
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ Backend service is running on port 8081"
else
    echo "❌ Backend service is not running. Please start it first."
    exit 1
fi

# Check if frontend is running
echo "2. Checking frontend service..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend service is running on port 3000"
else
    echo "❌ Frontend service is not running. Please start it first."
    exit 1
fi

# Test API endpoints
echo "3. Testing API endpoints..."

# Test health endpoint
echo "   - Testing /health endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:8081/health)
if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    echo "   ✅ Health endpoint working"
else
    echo "   ❌ Health endpoint failed"
fi

# Test metrics endpoint
echo "   - Testing /metrics endpoint..."
METRICS_RESPONSE=$(curl -s http://localhost:8081/metrics)
if echo "$METRICS_RESPONSE" | grep -q "totalRules"; then
    echo "   ✅ Metrics endpoint working"
else
    echo "   ❌ Metrics endpoint failed"
fi

# Test rules endpoint
echo "   - Testing /api/sanitization/rules endpoint..."
RULES_RESPONSE=$(curl -s http://localhost:8081/api/sanitization/rules)
if echo "$RULES_RESPONSE" | grep -q "rules"; then
    echo "   ✅ Rules endpoint working"
    RULE_COUNT=$(echo "$RULES_RESPONSE" | jq '.rules | length' 2>/dev/null || echo "unknown")
    echo "   📊 Found $RULE_COUNT rules"
else
    echo "   ❌ Rules endpoint failed"
fi

# Test reload endpoint
echo "   - Testing /api/sanitization/rules/reload endpoint..."
RELOAD_RESPONSE=$(curl -s -X POST http://localhost:8081/api/sanitization/rules/reload)
if [ $? -eq 0 ]; then
    echo "   ✅ Reload endpoint working"
else
    echo "   ❌ Reload endpoint failed"
fi

echo ""
echo "🎉 Frontend Testing Complete!"
echo ""
echo "📱 Access the application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8081"
echo ""
echo "🔧 Available pages:"
echo "   - Dashboard: http://localhost:3000/"
echo "   - Rules Management: http://localhost:3000/rules"
echo "   - Configuration: http://localhost:3000/config"
echo ""
echo "✨ Features to test:"
echo "   1. Dashboard - View service status and metrics"
echo "   2. Rules List - Search, filter, and view rules"
echo "   3. Configuration - Manage global settings"
echo "   4. Export functionality - Download configuration"
echo "   5. Reload functionality - Refresh from config file"
echo "   6. Responsive design - Test on different screen sizes"
