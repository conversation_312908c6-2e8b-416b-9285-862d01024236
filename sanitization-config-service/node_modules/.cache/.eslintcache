[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts": "4"}, {"size": 554, "mtime": 1752811811415, "results": "5", "hashOfConfig": "6"}, {"size": 425, "mtime": 1752811811418, "results": "7", "hashOfConfig": "6"}, {"size": 9809, "mtime": 1752812546963, "results": "8", "hashOfConfig": "6"}, {"size": 12417, "mtime": 1752812726008, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jk5nvp", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx", ["22", "23", "24", "25", "26"], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts", [], [], {"ruleId": "27", "severity": 1, "message": "28", "line": 5, "column": 3, "nodeType": "29", "messageId": "30", "endLine": 5, "endColumn": 7}, {"ruleId": "27", "severity": 1, "message": "31", "line": 8, "column": 3, "nodeType": "29", "messageId": "30", "endLine": 8, "endColumn": 9}, {"ruleId": "27", "severity": 1, "message": "32", "line": 9, "column": 3, "nodeType": "29", "messageId": "30", "endLine": 9, "endColumn": 7}, {"ruleId": "27", "severity": 1, "message": "33", "line": 15, "column": 3, "nodeType": "29", "messageId": "30", "endLine": 15, "endColumn": 11}, {"ruleId": "27", "severity": 1, "message": "34", "line": 18, "column": 30, "nodeType": "29", "messageId": "30", "endLine": 18, "endColumn": 46}, "@typescript-eslint/no-unused-vars", "'Plus' is defined but never used.", "Identifier", "unusedVar", "'Upload' is defined but never used.", "'Edit' is defined but never used.", "'Settings' is defined but never used.", "'SanitizationRule' is defined but never used."]