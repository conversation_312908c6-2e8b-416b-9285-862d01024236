# 快速配置指南

## 🚀 5分钟快速上手

### 1. 启动服务
```bash
# 开发环境 - 一键启动前后端
./start-dev.sh

# 或手动启动
SERVER_PORT=8081 go run main.go  # 后端
cd frontend && npm start         # 前端
```

### 2. 访问界面
- 前端管理界面: http://localhost:3000
- 后端API: http://localhost:8081

### 3. 基础配置

#### 环境变量（可选）
```bash
export SERVER_PORT=8081          # 服务端口
export LOG_LEVEL=info           # 日志级别
export AUTH_ENABLED=false      # 认证开关
```

## 📝 常用规则模板

### 密码字段脱敏
```json
{
  "id": "passwords",
  "name": "密码字段",
  "type": "FIELD_NAME",
  "severity": "CRITICAL",
  "enabled": true,
  "priority": 10,
  "fieldNames": ["password", "passwd", "pwd", "secret"],
  "maskValue": "********"
}
```

### 手机号脱敏（保留前3后4位）
```json
{
  "id": "phone-numbers",
  "name": "手机号码",
  "type": "PATTERN",
  "severity": "HIGH",
  "enabled": true,
  "priority": 20,
  "pattern": "(1[3-9]\\d)(\\d{4})(\\d{4})",
  "maskValue": "$1****$3"
}
```

### 邮箱脱敏（保留前3位和域名）
```json
{
  "id": "emails",
  "name": "邮箱地址", 
  "type": "PATTERN",
  "severity": "MEDIUM",
  "enabled": true,
  "priority": 30,
  "pattern": "([a-zA-Z0-9._%+-]{1,3})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})",
  "maskValue": "$1***@$2"
}
```

### 身份证脱敏（保留前6后4位）
```json
{
  "id": "id-cards",
  "name": "身份证号",
  "type": "PATTERN", 
  "severity": "HIGH",
  "enabled": true,
  "priority": 15,
  "pattern": "(\\d{6})\\d{8}(\\d{4})",
  "maskValue": "$1********$2"
}
```

### 银行卡脱敏（保留后4位）
```json
{
  "id": "bank-cards",
  "name": "银行卡号",
  "type": "PATTERN",
  "severity": "HIGH", 
  "enabled": true,
  "priority": 25,
  "pattern": "\\d{12,15}(\\d{4})",
  "maskValue": "************$1"
}
```

## 🎯 规则类型速查

| 类型 | 用途 | 配置字段 | 示例 |
|------|------|----------|------|
| `FIELD_NAME` | 字段名匹配 | `fieldNames` | 密码、密钥字段 |
| `PATTERN` | 正则匹配 | `pattern` | 手机号、邮箱 |
| `CONTENT_TYPE` | 内容类型 | `contentTypes` | JSON、XML数据 |
| `CUSTOM` | 自定义逻辑 | `conditions` | 业务特定规则 |

## 🔧 严重程度设置

| 级别 | 优先级范围 | 适用数据 | 颜色 |
|------|------------|----------|------|
| `CRITICAL` | 1-20 | 密码、密钥 | 🔴 |
| `HIGH` | 21-50 | 身份证、银行卡 | 🟠 |
| `MEDIUM` | 51-100 | 手机号、邮箱 | 🟡 |
| `LOW` | 101+ | 姓名、昵称 | 🟢 |

## 📋 完整配置示例

```json
{
  "version": "1.0.0",
  "enabled": true,
  "markersEnabled": false,
  "markerFormat": "BRACKET",
  "rules": [
    {
      "id": "critical-passwords",
      "name": "关键密码",
      "type": "FIELD_NAME",
      "severity": "CRITICAL",
      "enabled": true,
      "priority": 10,
      "fieldNames": ["password", "secret", "token"],
      "maskValue": "********"
    },
    {
      "id": "phone-pattern",
      "name": "手机号码",
      "type": "PATTERN", 
      "severity": "HIGH",
      "enabled": true,
      "priority": 20,
      "pattern": "(1[3-9]\\d)(\\d{4})(\\d{4})",
      "maskValue": "$1****$3"
    },
    {
      "id": "email-pattern",
      "name": "邮箱地址",
      "type": "PATTERN",
      "severity": "MEDIUM", 
      "enabled": true,
      "priority": 30,
      "pattern": "([\\w._%+-]{1,3})[\\w._%+-]*@([\\w.-]+\\.[a-zA-Z]{2,})",
      "maskValue": "$1***@$2"
    }
  ]
}
```

## 🎨 Web界面操作

### 创建规则
1. 访问 http://localhost:3000
2. 点击 "Rules" 标签页
3. 点击 "Create Rule" 按钮
4. 填写规则信息并测试
5. 保存规则

### 批量操作
1. 选择多个规则（勾选框）
2. 使用批量操作按钮：
   - Enable: 批量启用
   - Disable: 批量禁用  
   - Delete: 批量删除

### 导入导出
- **导出**: 点击 "Export" 按钮下载配置文件
- **导入**: 点击 "Import" 按钮上传配置文件

## 🧪 测试验证

### API测试
```bash
# 获取所有规则
curl http://localhost:8081/api/sanitization/rules

# 测试规则
curl -X POST http://localhost:8081/api/sanitization/rules/validate \
  -H "Content-Type: application/json" \
  -d '{
    "rule": {
      "type": "PATTERN",
      "pattern": "(1[3-9]\\d)(\\d{4})(\\d{4})",
      "maskValue": "$1****$3"
    },
    "testInput": "13812345678"
  }'
```

### 使用测试脚本
```bash
# 后端API测试
./test_service.sh

# 前端功能测试  
./test-frontend.sh
```

## ⚡ 常见问题

### Q: 规则不生效？
A: 检查规则是否启用，优先级是否正确，全局开关是否开启

### Q: 正则表达式报错？
A: 使用Web界面的测试功能验证正则表达式

### Q: 性能问题？
A: 简化复杂正则表达式，调整规则优先级

### Q: 端口冲突？
A: 修改 `SERVER_PORT` 环境变量

## 📚 更多资源

- [详细配置文档](./CONFIGURATION_GUIDE.md)
- [功能特性说明](./ENHANCED_FEATURES.md)
- [API接口文档](./README_EN.md)
- [项目总结](./PROJECT_SUMMARY.md)
