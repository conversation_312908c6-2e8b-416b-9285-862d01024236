{"version": "1.0.0", "enabled": true, "markersEnabled": false, "markerFormat": "BRACKET", "timestamp": 1703123456789, "rules": [{"id": "critical-passwords", "name": "关键密码字段", "description": "完全脱敏所有密码相关字段，包括password、secret、token等", "type": "FIELD_NAME", "severity": "CRITICAL", "enabled": true, "priority": 10, "fieldNames": ["password", "passwd", "pwd", "secret", "privateKey", "token", "accessToken", "refreshToken", "<PERSON><PERSON><PERSON><PERSON>"], "maskValue": "********", "markerType": "PASSWORD", "preserveFormat": false, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "id-card-numbers", "name": "身份证号码", "description": "脱敏身份证号码，保留前6位和后4位", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 15, "fieldNames": [], "pattern": "(\\d{6})\\d{8}(\\d{4})", "contentTypes": [], "maskValue": "$1********$2", "markerType": "ID_CARD", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "phone-numbers", "name": "手机号码", "description": "脱敏手机号码，保留前3位和后4位", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 20, "fieldNames": [], "pattern": "(1[3-9]\\d)(\\d{4})(\\d{4})", "contentTypes": [], "maskValue": "$1****$3", "markerType": "PHONE", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "bank-card-numbers", "name": "银行卡号", "description": "脱敏银行卡号，只保留后4位", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 25, "fieldNames": [], "pattern": "\\d{12,15}(\\d{4})", "contentTypes": [], "maskValue": "************$1", "markerType": "BANK_CARD", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "email-addresses", "name": "邮箱地址", "description": "脱敏邮箱地址，保留前3位字符和完整域名", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 30, "fieldNames": [], "pattern": "([a-zA-Z0-9._%+-]{1,3})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})", "contentTypes": [], "maskValue": "$1***@$2", "markerType": "EMAIL", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "ip-addresses", "name": "IP地址", "description": "脱敏IP地址，保留前两段", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 40, "fieldNames": [], "pattern": "(\\d{1,3}\\.\\d{1,3})\\.(\\d{1,3}\\.\\d{1,3})", "contentTypes": [], "maskValue": "$1.***.***", "markerType": "IP", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "chinese-names", "name": "中文姓名", "description": "脱敏中文姓名，保留姓氏", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 50, "fieldNames": [], "pattern": "([\\u4e00-\\u9fa5])[\\u4e00-\\u9fa5]{1,}", "contentTypes": [], "maskValue": "$1**", "markerType": "NAME", "preserveFormat": true, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "address-info", "name": "地址信息", "description": "脱敏详细地址信息", "type": "FIELD_NAME", "severity": "MEDIUM", "enabled": true, "priority": 60, "fieldNames": ["address", "addr", "location", "home<PERSON>dd<PERSON>", "workAddress", "detail<PERSON><PERSON><PERSON>"], "pattern": "", "contentTypes": [], "maskValue": "***地址信息***", "markerType": "ADDRESS", "preserveFormat": false, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "json-sensitive-data", "name": "JSON敏感数据", "description": "处理JSON格式中的敏感字段", "type": "CONTENT_TYPE", "severity": "HIGH", "enabled": true, "priority": 35, "fieldNames": ["creditCard", "ssn", "passport", "driverLicense"], "pattern": "", "contentTypes": ["application/json", "application/x-www-form-urlencoded"], "maskValue": "***SENSITIVE***", "markerType": "JSON_SENSITIVE", "preserveFormat": false, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}, {"id": "user-ids", "name": "用户标识", "description": "脱敏用户ID和相关标识符", "type": "FIELD_NAME", "severity": "LOW", "enabled": true, "priority": 100, "fieldNames": ["userId", "uid", "openId", "unionId", "memberId"], "pattern": "", "contentTypes": [], "maskValue": "***ID***", "markerType": "USER_ID", "preserveFormat": false, "preserveLength": 0, "includeServices": [], "excludeServices": [], "conditions": {}}]}