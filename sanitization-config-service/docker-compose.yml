services:
  sanitization-config-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sanitization-config-service
    ports:
      - "3000:8080"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - REACT_APP_VERSION=1.0.0
    networks:
      - sanitization-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    labels:
      - "com.docker.compose.service=sanitization-config-service"
      - "version=1.0.0"

  # 可选：反向代理服务
  nginx-proxy:
    image: nginx:alpine
    container_name: sanitization-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      sanitization-config-service:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - sanitization-network
    profiles:
      - proxy
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80

networks:
  sanitization-network:
    driver: bridge
    name: sanitization-network

volumes:
  sanitization-logs:
    driver: local
    name: sanitization-logs
