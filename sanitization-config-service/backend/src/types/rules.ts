export enum RuleType {
  FIELD_NAME = 'FIELD_NAME',
  PATTERN = 'PATTERN'
}

export enum Severity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum MarkerType {
  PASSWORD = 'PASSWORD',
  EMAIL = 'EMAIL',
  CREDIT_CARD = 'CREDIT_CARD',
  PHONE = 'PHONE',
  USERNAME = 'USERNAME',
  SSN = 'SSN',
  API_KEY = 'API_KEY',
  DB_PASSWORD = 'DB_PASSWORD',
  PAYMENT_DATA = 'PAYMENT_DATA',
  PII = 'PII'
}

export enum MarkerFormat {
  BRACKET = 'BRACKET',
  TAG = 'TAG',
  NONE = 'NONE'
}

export interface SanitizationRule {
  id: string;
  name: string;
  description: string;
  type: RuleType;
  severity: Severity;
  enabled: boolean;
  priority: number;
  fieldNames?: string[];
  pattern?: string;
  contentTypes?: string[];
  maskValue: string;
  markerType: MarkerType;
  preserveFormat: boolean;
  preserveLength: number;
  includeServices?: string[];
}

export interface GlobalSettings {
  defaultMaskValue: string;
  enableLogging: boolean;
  enableMetrics: boolean;
  logLevel: string;
  maxRulesPriority: number;
}

export interface RulesConfig {
  version: string;
  timestamp: number;
  enabled: boolean;
  markersEnabled: boolean;
  markerFormat: MarkerFormat;
  rules: SanitizationRule[];
  globalSettings: GlobalSettings;
}
