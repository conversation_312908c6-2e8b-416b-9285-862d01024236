# 用户使用指南

## 概述

脱敏配置服务是一个用于管理数据脱敏规则的Web应用程序。通过直观的界面，您可以轻松创建、编辑和管理各种数据脱敏规则，保护敏感信息的安全。

## 快速开始

### 访问应用

1. 打开浏览器
2. 访问应用地址（如：http://localhost:3000）
3. 应用将自动加载默认的脱敏规则

### 界面概览

应用界面主要包含以下部分：

- **顶部导航栏** - 显示应用标题和全局控制按钮
- **搜索和统计区域** - 搜索规则和查看统计信息
- **规则列表** - 显示所有脱敏规则的卡片列表

## 主要功能

### 1. 全局开关

位于顶部导航栏右侧的全局开关可以一键启用或禁用所有脱敏功能：

- **绿色按钮** - 脱敏功能已启用
- **红色按钮** - 脱敏功能已禁用

点击按钮可以切换状态。

### 2. 规则搜索

在搜索框中输入关键词可以快速查找规则：

- 支持按规则名称搜索
- 支持按规则描述搜索
- 支持按规则ID搜索
- 实时搜索，无需按回车

### 3. 统计信息

搜索框右侧显示规则统计：

- **总计** - 所有规则的数量
- **活跃** - 已启用规则的数量（绿色）
- **非活跃** - 已禁用规则的数量（红色）

### 4. 规则管理

#### 查看规则

每个规则以卡片形式显示，包含：

- **规则名称** - 规则的显示名称
- **严重程度标签** - 颜色编码的优先级
  - 🔴 关键 (CRITICAL)
  - 🟠 高 (HIGH)
  - 🟡 中等 (MEDIUM)
  - 🟢 低 (LOW)
- **规则类型标签** - 规则的匹配类型
- **规则描述** - 详细说明
- **技术信息** - 规则ID、匹配模式、字段名等

#### 启用/禁用规则

每个规则卡片右侧有控制按钮：

- **👁️ 眼睛图标（绿色）** - 规则已启用，点击可禁用
- **👁️‍🗨️ 眼睛图标（灰色）** - 规则已禁用，点击可启用

#### 删除规则

每个规则卡片右侧的垃圾桶图标可以删除规则：

- 点击后会弹出确认对话框
- 确认后规则将被永久删除
- 删除操作不可撤销

### 5. 数据导出

点击顶部导航栏的"导出"按钮可以下载当前所有规则配置：

- 文件格式：JSON
- 文件名：sanitization-rules.json
- 包含所有规则和配置信息

## 默认规则说明

系统预置了三个常用的脱敏规则：

### 1. 手机号脱敏

- **名称**：手机号脱敏
- **类型**：正则匹配 (PATTERN)
- **严重程度**：高 (HIGH)
- **匹配模式**：`1[3-9]\\d{9}`
- **脱敏效果**：`***-****-****`
- **适用字段**：phone, mobile, phoneNumber

### 2. 邮箱脱敏

- **名称**：邮箱脱敏
- **类型**：正则匹配 (PATTERN)
- **严重程度**：中等 (MEDIUM)
- **匹配模式**：邮箱格式正则表达式
- **脱敏效果**：`***@***.***`
- **适用字段**：email, emailAddress

### 3. 身份证脱敏

- **名称**：身份证脱敏
- **类型**：正则匹配 (PATTERN)
- **严重程度**：关键 (CRITICAL)
- **匹配模式**：中国身份证号格式
- **脱敏效果**：`****-****-****-****`
- **适用字段**：idCard, identityCard, id

## 规则类型说明

### 1. 字段名匹配 (FIELD_NAME)

根据数据字段名称进行匹配：

- 适用于已知字段名的场景
- 配置简单，性能较好
- 示例：匹配名为"password"的字段

### 2. 正则匹配 (PATTERN)

使用正则表达式匹配数据内容：

- 适用于复杂的数据格式
- 灵活性最高，但性能相对较低
- 示例：匹配手机号、邮箱等格式

### 3. 内容类型 (CONTENT_TYPE)

根据数据的内容类型进行匹配：

- 适用于特定数据类型的场景
- 示例：匹配所有图片、文档等

### 4. 自定义 (CUSTOM)

自定义匹配逻辑：

- 适用于特殊业务场景
- 需要额外的开发支持

## 严重程度说明

### 关键 (CRITICAL) 🔴

- 最高优先级
- 包含极其敏感的个人信息
- 示例：身份证号、银行卡号
- 建议：必须脱敏，不允许明文存储

### 高 (HIGH) 🟠

- 高优先级
- 包含重要的个人信息
- 示例：手机号、家庭地址
- 建议：强烈建议脱敏

### 中等 (MEDIUM) 🟡

- 中等优先级
- 包含一般的个人信息
- 示例：邮箱地址、姓名
- 建议：建议脱敏

### 低 (LOW) 🟢

- 低优先级
- 包含较少敏感的信息
- 示例：用户昵称、公开信息
- 建议：可选择性脱敏

## 数据存储说明

### 本地存储

- 所有数据存储在浏览器的LocalStorage中
- 数据仅在当前浏览器中有效
- 清除浏览器数据会导致配置丢失

### 数据备份

建议定期导出配置文件进行备份：

1. 点击"导出"按钮
2. 保存JSON文件到安全位置
3. 需要时可以通过导入功能恢复

### 隐私保护

- 所有数据仅存储在用户本地
- 不会上传到任何服务器
- 保证数据隐私和安全

## 最佳实践

### 1. 规则命名

- 使用清晰、描述性的名称
- 包含规则的用途和范围
- 示例：`用户手机号脱敏`、`订单邮箱脱敏`

### 2. 严重程度设置

- 根据数据敏感性合理设置
- 关键信息必须设为CRITICAL
- 定期审查和调整级别

### 3. 规则维护

- 定期检查规则的有效性
- 及时更新过时的规则
- 删除不再使用的规则

### 4. 数据备份

- 定期导出配置文件
- 在重要变更前进行备份
- 保存多个版本的备份文件

## 故障排除

### 常见问题

#### 1. 规则不生效

- 检查全局开关是否启用
- 确认规则本身是否启用
- 验证匹配模式是否正确

#### 2. 搜索无结果

- 检查搜索关键词是否正确
- 尝试使用部分关键词
- 清空搜索框查看所有规则

#### 3. 数据丢失

- 检查浏览器是否清除了存储数据
- 尝试从备份文件恢复
- 重新创建必要的规则

#### 4. 导出失败

- 检查浏览器是否阻止了下载
- 尝试在不同浏览器中操作
- 确认有足够的磁盘空间

### 获取帮助

如果遇到问题：

1. 查看浏览器控制台的错误信息
2. 尝试刷新页面
3. 清除浏览器缓存
4. 联系技术支持团队

## 更新日志

### 版本 1.0.0

- 初始版本发布
- 支持基本的规则管理功能
- 提供默认脱敏规则
- 实现本地数据存储
