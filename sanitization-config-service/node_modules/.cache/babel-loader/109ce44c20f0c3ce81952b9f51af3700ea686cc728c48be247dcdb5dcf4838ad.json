{"ast": null, "code": "const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({\n      onCLS,\n      onFID,\n      onFCP,\n      onLCP,\n      onTTFB\n    }) => {\n      onCLS(onPerfEntry);\n      onFID(onPerfEntry);\n      onFCP(onPerfEntry);\n      onLCP(onPerfEntry);\n      onTTFB(onPerfEntry);\n    });\n  }\n};\nexport default reportWebVitals;", "map": {"version": 3, "names": ["reportWebVitals", "onPerfEntry", "Function", "then", "onCLS", "onFID", "onFCP", "onLCP", "onTTFB"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts"], "sourcesContent": ["import { Metric } from 'web-vitals';\n\nconst reportWebVitals = (onPerfEntry?: (metric: Metric) => void) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ onCLS, onFID, onFCP, onLCP, onTTFB }) => {\n      onCLS(onPerfEntry);\n      onFID(onPerfEntry);\n      onFCP(onPerfEntry);\n      onLCP(onPerfEntry);\n      onTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n"], "mappings": "AAEA,MAAMA,eAAe,GAAIC,WAAsC,IAAK;EAClE,IAAIA,WAAW,IAAIA,WAAW,YAAYC,QAAQ,EAAE;IAClD,MAAM,CAAC,YAAY,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MAAEC;IAAO,CAAC,KAAK;MACpEJ,KAAK,CAACH,WAAW,CAAC;MAClBI,KAAK,CAACJ,WAAW,CAAC;MAClBK,KAAK,CAACL,WAAW,CAAC;MAClBM,KAAK,CAACN,WAAW,CAAC;MAClBO,MAAM,CAACP,WAAW,CAAC;IACrB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}