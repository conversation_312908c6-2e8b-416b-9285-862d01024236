{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"m14.305 7.53.923-.382\",\n  key: \"1mlnsw\"\n}], [\"path\", {\n  d: \"m15.228 4.852-.923-.383\",\n  key: \"82mpwg\"\n}], [\"path\", {\n  d: \"m16.852 3.228-.383-.924\",\n  key: \"ln4sir\"\n}], [\"path\", {\n  d: \"m16.852 8.772-.383.923\",\n  key: \"1dejw0\"\n}], [\"path\", {\n  d: \"m19.148 3.228.383-.924\",\n  key: \"192kgf\"\n}], [\"path\", {\n  d: \"m19.53 9.696-.382-.924\",\n  key: \"fiavlr\"\n}], [\"path\", {\n  d: \"m20.772 4.852.924-.383\",\n  key: \"1j8mgp\"\n}], [\"path\", {\n  d: \"m20.772 7.148.924.383\",\n  key: \"zix9be\"\n}], [\"path\", {\n  d: \"M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"1tnzv8\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}]];\nconst MonitorCog = createLucideIcon(\"monitor-cog\", __iconNode);\nexport { __iconNode, MonitorCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "MonitorCog", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/monitor-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 17v4', key: '1riwvh' }],\n  ['path', { d: 'm14.305 7.53.923-.382', key: '1mlnsw' }],\n  ['path', { d: 'm15.228 4.852-.923-.383', key: '82mpwg' }],\n  ['path', { d: 'm16.852 3.228-.383-.924', key: 'ln4sir' }],\n  ['path', { d: 'm16.852 8.772-.383.923', key: '1dejw0' }],\n  ['path', { d: 'm19.148 3.228.383-.924', key: '192kgf' }],\n  ['path', { d: 'm19.53 9.696-.382-.924', key: 'fiavlr' }],\n  ['path', { d: 'm20.772 4.852.924-.383', key: '1j8mgp' }],\n  ['path', { d: 'm20.772 7.148.924.383', key: 'zix9be' }],\n  ['path', { d: 'M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7', key: '1tnzv8' }],\n  ['path', { d: 'M8 21h8', key: '1ev6f3' }],\n  ['circle', { cx: '18', cy: '6', r: '3', key: '1h7g24' }],\n];\n\n/**\n * @component @name MonitorCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTd2NCIgLz4KICA8cGF0aCBkPSJtMTQuMzA1IDcuNTMuOTIzLS4zODIiIC8+CiAgPHBhdGggZD0ibTE1LjIyOCA0Ljg1Mi0uOTIzLS4zODMiIC8+CiAgPHBhdGggZD0ibTE2Ljg1MiAzLjIyOC0uMzgzLS45MjQiIC8+CiAgPHBhdGggZD0ibTE2Ljg1MiA4Ljc3Mi0uMzgzLjkyMyIgLz4KICA8cGF0aCBkPSJtMTkuMTQ4IDMuMjI4LjM4My0uOTI0IiAvPgogIDxwYXRoIGQ9Im0xOS41MyA5LjY5Ni0uMzgyLS45MjQiIC8+CiAgPHBhdGggZD0ibTIwLjc3MiA0Ljg1Mi45MjQtLjM4MyIgLz4KICA8cGF0aCBkPSJtMjAuNzcyIDcuMTQ4LjkyNC4zODMiIC8+CiAgPHBhdGggZD0iTTIyIDEzdjJhMiAyIDAgMCAxLTIgMkg0YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDciIC8+CiAgPHBhdGggZD0iTTggMjFoOCIgLz4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjYiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/monitor-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorCog = createLucideIcon('monitor-cog', __iconNode);\n\nexport default MonitorCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,UAAA,GAAaC,gBAAiB,gBAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}