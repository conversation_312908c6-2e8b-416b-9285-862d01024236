#!/bin/bash

# 脱敏配置检查脚本
# 用于验证配置文件的正确性和完整性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="${1:-config/rules.json}"
TEMPLATE_FILE="config/rules.template.json"

echo -e "${BLUE}🔍 脱敏配置检查工具${NC}"
echo "=================================="

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ 配置文件不存在: $CONFIG_FILE${NC}"
    if [ -f "$TEMPLATE_FILE" ]; then
        echo -e "${YELLOW}💡 建议复制模板文件: cp $TEMPLATE_FILE $CONFIG_FILE${NC}"
    fi
    exit 1
fi

echo -e "${GREEN}✅ 配置文件存在: $CONFIG_FILE${NC}"

# 检查JSON格式
echo -n "🔍 检查JSON格式... "
if jq empty "$CONFIG_FILE" 2>/dev/null; then
    echo -e "${GREEN}✅ 格式正确${NC}"
else
    echo -e "${RED}❌ JSON格式错误${NC}"
    echo "请检查配置文件的JSON语法"
    exit 1
fi

# 检查必需字段
echo "🔍 检查必需字段..."

# 检查全局配置
check_field() {
    local field=$1
    local file=$2
    if jq -e ".$field" "$file" > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ $field${NC}"
        return 0
    else
        echo -e "  ${RED}❌ $field 缺失${NC}"
        return 1
    fi
}

# 全局字段检查
GLOBAL_FIELDS=("version" "enabled" "rules")
for field in "${GLOBAL_FIELDS[@]}"; do
    check_field "$field" "$CONFIG_FILE"
done

# 检查规则数量
RULE_COUNT=$(jq '.rules | length' "$CONFIG_FILE")
echo -e "${BLUE}📊 规则数量: $RULE_COUNT${NC}"

if [ "$RULE_COUNT" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  没有配置任何规则${NC}"
else
    echo -e "${GREEN}✅ 已配置 $RULE_COUNT 个规则${NC}"
fi

# 检查每个规则的必需字段
echo "🔍 检查规则配置..."
RULE_FIELDS=("id" "name" "type" "severity" "enabled" "priority" "maskValue")

for i in $(seq 0 $((RULE_COUNT-1))); do
    echo -e "${BLUE}  规则 $((i+1)):${NC}"
    
    # 获取规则ID和名称
    RULE_ID=$(jq -r ".rules[$i].id" "$CONFIG_FILE")
    RULE_NAME=$(jq -r ".rules[$i].name" "$CONFIG_FILE")
    echo -e "    ID: $RULE_ID, 名称: $RULE_NAME"
    
    # 检查必需字段
    for field in "${RULE_FIELDS[@]}"; do
        if jq -e ".rules[$i].$field" "$CONFIG_FILE" > /dev/null 2>&1; then
            echo -e "    ${GREEN}✅ $field${NC}"
        else
            echo -e "    ${RED}❌ $field 缺失${NC}"
        fi
    done
    
    # 检查规则类型特定字段
    RULE_TYPE=$(jq -r ".rules[$i].type" "$CONFIG_FILE")
    case "$RULE_TYPE" in
        "FIELD_NAME")
            if jq -e ".rules[$i].fieldNames" "$CONFIG_FILE" > /dev/null 2>&1; then
                FIELD_COUNT=$(jq '.rules['$i'].fieldNames | length' "$CONFIG_FILE")
                echo -e "    ${GREEN}✅ fieldNames ($FIELD_COUNT 个字段)${NC}"
            else
                echo -e "    ${RED}❌ FIELD_NAME类型需要fieldNames字段${NC}"
            fi
            ;;
        "PATTERN")
            if jq -e ".rules[$i].pattern" "$CONFIG_FILE" > /dev/null 2>&1; then
                echo -e "    ${GREEN}✅ pattern${NC}"
            else
                echo -e "    ${RED}❌ PATTERN类型需要pattern字段${NC}"
            fi
            ;;
        "CONTENT_TYPE")
            if jq -e ".rules[$i].contentTypes" "$CONFIG_FILE" > /dev/null 2>&1; then
                TYPE_COUNT=$(jq '.rules['$i'].contentTypes | length' "$CONFIG_FILE")
                echo -e "    ${GREEN}✅ contentTypes ($TYPE_COUNT 个类型)${NC}"
            else
                echo -e "    ${RED}❌ CONTENT_TYPE类型需要contentTypes字段${NC}"
            fi
            ;;
    esac
    
    echo ""
done

# 检查规则ID唯一性
echo "🔍 检查规则ID唯一性..."
DUPLICATE_IDS=$(jq -r '.rules[].id' "$CONFIG_FILE" | sort | uniq -d)
if [ -z "$DUPLICATE_IDS" ]; then
    echo -e "${GREEN}✅ 所有规则ID唯一${NC}"
else
    echo -e "${RED}❌ 发现重复的规则ID:${NC}"
    echo "$DUPLICATE_IDS"
fi

# 检查优先级设置
echo "🔍 检查优先级设置..."
jq -r '.rules[] | "\(.priority) \(.id) \(.name)"' "$CONFIG_FILE" | sort -n | while read -r line; do
    echo "  $line"
done

# 统计信息
echo -e "${BLUE}📊 配置统计:${NC}"
echo "  总规则数: $RULE_COUNT"

# 按类型统计
echo "  按类型分布:"
jq -r '.rules[] | .type' "$CONFIG_FILE" | sort | uniq -c | while read -r count type; do
    echo "    $type: $count"
done

# 按严重程度统计
echo "  按严重程度分布:"
jq -r '.rules[] | .severity' "$CONFIG_FILE" | sort | uniq -c | while read -r count severity; do
    echo "    $severity: $count"
done

# 启用状态统计
ENABLED_COUNT=$(jq '[.rules[] | select(.enabled == true)] | length' "$CONFIG_FILE")
DISABLED_COUNT=$(jq '[.rules[] | select(.enabled == false)] | length' "$CONFIG_FILE")
echo "  启用状态:"
echo "    启用: $ENABLED_COUNT"
echo "    禁用: $DISABLED_COUNT"

# 全局状态
GLOBAL_ENABLED=$(jq -r '.enabled' "$CONFIG_FILE")
echo "  全局状态: $([ "$GLOBAL_ENABLED" = "true" ] && echo "启用" || echo "禁用")"

echo ""
echo -e "${GREEN}🎉 配置检查完成!${NC}"

# 提供建议
echo -e "${BLUE}💡 建议:${NC}"
echo "  1. 定期备份配置文件"
echo "  2. 在生产环境部署前进行测试"
echo "  3. 使用Web界面的验证功能测试规则"
echo "  4. 监控规则的性能影响"

# 如果有模板文件，提供对比信息
if [ -f "$TEMPLATE_FILE" ] && [ "$CONFIG_FILE" != "$TEMPLATE_FILE" ]; then
    echo ""
    echo -e "${YELLOW}📋 模板对比:${NC}"
    TEMPLATE_COUNT=$(jq '.rules | length' "$TEMPLATE_FILE")
    echo "  当前配置: $RULE_COUNT 个规则"
    echo "  模板文件: $TEMPLATE_COUNT 个规则"
    
    if [ "$RULE_COUNT" -lt "$TEMPLATE_COUNT" ]; then
        echo -e "  ${YELLOW}💡 模板文件包含更多规则示例，可参考添加${NC}"
    fi
fi

echo ""
echo -e "${BLUE}🔗 相关文档:${NC}"
echo "  - 快速配置: ./QUICK_CONFIG.md"
echo "  - 详细配置: ./CONFIGURATION_GUIDE.md"
echo "  - 文档索引: ./DOCS_INDEX.md"
