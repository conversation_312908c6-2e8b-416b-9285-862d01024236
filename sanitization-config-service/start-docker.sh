#!/bin/bash

# 脱敏配置服务 Docker Compose 启动脚本
# 用途：简化 Docker Compose 部署流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    print_message $BLUE "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "错误: Docker Compose 未安装"
        exit 1
    fi
    
    print_message $GREEN "✓ Docker 和 Docker Compose 已安装"
}

# 构建和启动服务
start_services() {
    print_message $BLUE "构建和启动脱敏配置服务..."
    
    # 构建镜像
    docker-compose build --no-cache
    
    # 启动服务
    docker-compose up -d
    
    print_message $GREEN "✓ 服务启动成功"
}

# 检查服务状态
check_services() {
    print_message $BLUE "检查服务状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        print_message $GREEN "✓ 容器运行正常"
    else
        print_message $RED "✗ 容器启动失败"
        docker-compose logs
        exit 1
    fi
    
    # 检查健康状态
    print_message $BLUE "等待服务健康检查..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            print_message $GREEN "✓ 服务健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_message $RED "✗ 服务健康检查失败"
            docker-compose logs sanitization-config-service
            exit 1
        fi
        
        print_message $YELLOW "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
}

# 显示访问信息
show_access_info() {
    print_message $GREEN "🎉 脱敏配置服务部署成功！"
    echo
    print_message $BLUE "访问信息:"
    echo "  本地访问: http://localhost:3000"
    echo "  健康检查: http://localhost:3000/health"
    echo
    print_message $BLUE "管理命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo "  查看状态: docker-compose ps"
    echo
}

# 主函数
main() {
    print_message $GREEN "=== 脱敏配置服务 Docker 部署 ==="
    echo
    
    check_requirements
    start_services
    check_services
    show_access_info
    
    print_message $GREEN "部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    "start"|"")
        main
        ;;
    "stop")
        print_message $BLUE "停止服务..."
        docker-compose down
        print_message $GREEN "✓ 服务已停止"
        ;;
    "restart")
        print_message $BLUE "重启服务..."
        docker-compose restart
        print_message $GREEN "✓ 服务已重启"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "clean")
        print_message $BLUE "清理资源..."
        docker-compose down -v
        docker image prune -f
        print_message $GREEN "✓ 清理完成"
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo
        echo "命令:"
        echo "  start    启动服务 (默认)"
        echo "  stop     停止服务"
        echo "  restart  重启服务"
        echo "  logs     查看日志"
        echo "  status   查看状态"
        echo "  clean    清理资源"
        echo "  help     显示帮助"
        ;;
    *)
        print_message $RED "未知命令: $1"
        print_message $BLUE "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
