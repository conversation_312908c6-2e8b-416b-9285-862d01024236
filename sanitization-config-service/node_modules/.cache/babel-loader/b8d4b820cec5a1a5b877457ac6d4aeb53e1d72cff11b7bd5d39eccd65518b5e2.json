{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\",\n  key: \"qn84l0\"\n}], [\"path\", {\n  d: \"M9 12h6\",\n  key: \"1c52cq\"\n}], [\"path\", {\n  d: \"M12 9v6\",\n  key: \"199k2o\"\n}]];\nconst TicketPlus = createLucideIcon(\"ticket-plus\", __iconNode);\nexport { __iconNode, TicketPlus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TicketPlus", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/ticket-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z',\n      key: 'qn84l0',\n    },\n  ],\n  ['path', { d: 'M9 12h6', key: '1c52cq' }],\n  ['path', { d: 'M12 9v6', key: '199k2o' }],\n];\n\n/**\n * @component @name TicketPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiA5YTMgMyAwIDAgMSAwIDZ2MmEyIDIgMCAwIDAgMiAyaDE2YTIgMiAwIDAgMCAyLTJ2LTJhMyAzIDAgMCAxIDAtNlY3YTIgMiAwIDAgMC0yLTJINGEyIDIgMCAwIDAtMiAyWiIgLz4KICA8cGF0aCBkPSJNOSAxMmg2IiAvPgogIDxwYXRoIGQ9Ik0xMiA5djYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/ticket-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TicketPlus = createLucideIcon('ticket-plus', __iconNode);\n\nexport default TicketPlus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}