package handlers

import (
	"net/http"
	"time"

	"sanitization-config-service/models"
	"sanitization-config-service/service"

	"github.com/gin-gonic/gin"
)

// RulesHandler handles HTTP requests for sanitization rules
type RulesHandler struct {
	ruleService *service.RuleService
}

// NewRulesHandler creates a new rules handler
func NewRulesHandler(ruleService *service.RuleService) *RulesHandler {
	return &RulesHandler{
		ruleService: ruleService,
	}
}

// GetRules returns sanitization rules
// @Summary Get sanitization rules
// @Description Get sanitization rules, optionally filtered by service name
// @Tags rules
// @Accept json
// @Produce json
// @Param X-Service-Name header string false "Service name for filtering rules"
// @Success 200 {object} models.SanitizationConfig
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules [get]
func (h *RulesHandler) GetRules(c *gin.Context) {
	serviceName := c.<PERSON>eader("X-Service-Name")

	var config interface{}
	if serviceName != "" {
		config = h.ruleService.GetConfigForService(serviceName)
	} else {
		config = h.ruleService.GetConfig()
	}

	c.JSON(http.StatusOK, config)
}

// ReloadRules reloads rules from configuration file
// @Summary Reload sanitization rules
// @Description Reload sanitization rules from the configuration file
// @Tags rules
// @Accept json
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/reload [post]
func (h *RulesHandler) ReloadRules(c *gin.Context) {
	if err := h.ruleService.ReloadConfig(); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to reload configuration",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "Configuration reloaded successfully",
		Timestamp: time.Now().Unix(),
	})
}

// ToggleGlobalSwitch toggles the global sanitization switch
// @Summary Toggle global sanitization switch
// @Description Enable or disable global sanitization
// @Tags rules
// @Accept json
// @Produce json
// @Param request body ToggleRequest true "Toggle request"
// @Success 200 {object} ToggleResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/toggle [post]
func (h *RulesHandler) ToggleGlobalSwitch(c *gin.Context) {
	var req ToggleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	if err := h.ruleService.SetGlobalEnabled(req.Enabled); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to toggle global switch",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	config := h.ruleService.GetConfig()
	c.JSON(http.StatusOK, ToggleResponse{
		Enabled:   config.Enabled,
		Message:   "Global sanitization switch updated successfully",
		Timestamp: time.Now().Unix(),
	})
}

// GetHealth returns service health status
// @Summary Health check
// @Description Get service health status
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /health [get]
func (h *RulesHandler) GetHealth(c *gin.Context) {
	config := h.ruleService.GetConfig()

	c.JSON(http.StatusOK, HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Unix(),
		Version:   config.Version,
		RuleCount: len(config.Rules),
	})
}

// GetMetrics returns service metrics
// @Summary Get service metrics
// @Description Get service metrics and statistics
// @Tags metrics
// @Accept json
// @Produce json
// @Success 200 {object} MetricsResponse
// @Router /metrics [get]
func (h *RulesHandler) GetMetrics(c *gin.Context) {
	config := h.ruleService.GetConfig()

	enabledRules := 0
	rulesByType := make(map[string]int)
	rulesBySeverity := make(map[string]int)

	for _, rule := range config.Rules {
		if rule.Enabled {
			enabledRules++
		}
		rulesByType[string(rule.Type)]++
		rulesBySeverity[string(rule.Severity)]++
	}

	c.JSON(http.StatusOK, MetricsResponse{
		TotalRules:      len(config.Rules),
		EnabledRules:    enabledRules,
		DisabledRules:   len(config.Rules) - enabledRules,
		RulesByType:     rulesByType,
		RulesBySeverity: rulesBySeverity,
		ConfigVersion:   config.Version,
		LastUpdated:     config.Timestamp,
	})
}

// CreateRule creates a new sanitization rule
// @Summary Create a new sanitization rule
// @Description Create a new sanitization rule
// @Tags rules
// @Accept json
// @Produce json
// @Param rule body models.SanitizationRule true "Rule to create"
// @Success 201 {object} RuleResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules [post]
func (h *RulesHandler) CreateRule(c *gin.Context) {
	var rule models.SanitizationRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	if err := h.ruleService.CreateRule(rule); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to create rule",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusCreated, RuleResponse{
		Rule:      rule,
		Message:   "Rule created successfully",
		Timestamp: time.Now().Unix(),
	})
}

// UpdateRule updates an existing sanitization rule
// @Summary Update a sanitization rule
// @Description Update an existing sanitization rule
// @Tags rules
// @Accept json
// @Produce json
// @Param id path string true "Rule ID"
// @Param rule body models.SanitizationRule true "Updated rule"
// @Success 200 {object} RuleResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/{id} [put]
func (h *RulesHandler) UpdateRule(c *gin.Context) {
	ruleID := c.Param("id")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   "Rule ID is required",
			Timestamp: time.Now().Unix(),
		})
		return
	}

	var rule models.SanitizationRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	rule.ID = ruleID
	if err := h.ruleService.UpdateRule(rule); err != nil {
		if err.Error() == "rule not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Rule not found",
				Message:   "Rule with ID " + ruleID + " not found",
				Timestamp: time.Now().Unix(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update rule",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusOK, RuleResponse{
		Rule:      rule,
		Message:   "Rule updated successfully",
		Timestamp: time.Now().Unix(),
	})
}

// DeleteRule deletes a sanitization rule
// @Summary Delete a sanitization rule
// @Description Delete a sanitization rule by ID
// @Tags rules
// @Accept json
// @Produce json
// @Param id path string true "Rule ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/{id} [delete]
func (h *RulesHandler) DeleteRule(c *gin.Context) {
	ruleID := c.Param("id")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   "Rule ID is required",
			Timestamp: time.Now().Unix(),
		})
		return
	}

	if err := h.ruleService.DeleteRule(ruleID); err != nil {
		if err.Error() == "rule not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Rule not found",
				Message:   "Rule with ID " + ruleID + " not found",
				Timestamp: time.Now().Unix(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to delete rule",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "Rule deleted successfully",
		Timestamp: time.Now().Unix(),
	})
}

// ToggleRule toggles a specific rule's enabled status
// @Summary Toggle rule enabled status
// @Description Enable or disable a specific rule
// @Tags rules
// @Accept json
// @Produce json
// @Param id path string true "Rule ID"
// @Param request body ToggleRequest true "Toggle request"
// @Success 200 {object} RuleResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/{id}/toggle [post]
func (h *RulesHandler) ToggleRule(c *gin.Context) {
	ruleID := c.Param("id")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   "Rule ID is required",
			Timestamp: time.Now().Unix(),
		})
		return
	}

	var req ToggleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	rule, err := h.ruleService.ToggleRule(ruleID, req.Enabled)
	if err != nil {
		if err.Error() == "rule not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Rule not found",
				Message:   "Rule with ID " + ruleID + " not found",
				Timestamp: time.Now().Unix(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to toggle rule",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusOK, RuleResponse{
		Rule:      *rule,
		Message:   "Rule toggled successfully",
		Timestamp: time.Now().Unix(),
	})
}

// BatchOperation performs batch operations on multiple rules
// @Summary Perform batch operations on rules
// @Description Enable, disable, or delete multiple rules at once
// @Tags rules
// @Accept json
// @Produce json
// @Param request body BatchOperationRequest true "Batch operation request"
// @Success 200 {object} BatchOperationResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/batch [post]
func (h *RulesHandler) BatchOperation(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	if len(req.RuleIDs) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   "At least one rule ID is required",
			Timestamp: time.Now().Unix(),
		})
		return
	}

	result, err := h.ruleService.BatchOperation(req.RuleIDs, req.Operation)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Batch operation failed",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusOK, BatchOperationResponse{
		SuccessCount: result.SuccessCount,
		FailedCount:  result.FailedCount,
		FailedRules:  result.FailedRules,
		Message:      "Batch operation completed",
		Timestamp:    time.Now().Unix(),
	})
}

// ValidateRule validates a rule configuration
// @Summary Validate rule configuration
// @Description Validate a rule and optionally test it with sample input
// @Tags rules
// @Accept json
// @Produce json
// @Param request body ValidateRuleRequest true "Validation request"
// @Success 200 {object} ValidateRuleResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/sanitization/rules/validate [post]
func (h *RulesHandler) ValidateRule(c *gin.Context) {
	var req ValidateRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	result := h.ruleService.ValidateRule(req.Rule, req.TestInput)

	c.JSON(http.StatusOK, ValidateRuleResponse{
		Valid:      result.Valid,
		Errors:     result.Errors,
		TestOutput: result.TestOutput,
		Message:    "Rule validation completed",
		Timestamp:  time.Now().Unix(),
	})
}

// ExportRules exports rules configuration
// @Summary Export rules configuration
// @Description Export the current rules configuration as JSON
// @Tags rules
// @Accept json
// @Produce json
// @Success 200 {object} models.SanitizationConfig
// @Router /api/sanitization/rules/export [get]
func (h *RulesHandler) ExportRules(c *gin.Context) {
	config := h.ruleService.GetConfig()

	c.Header("Content-Disposition", "attachment; filename=sanitization-rules.json")
	c.Header("Content-Type", "application/json")
	c.JSON(http.StatusOK, config)
}

// ImportRules imports rules configuration
// @Summary Import rules configuration
// @Description Import and replace the current rules configuration
// @Tags rules
// @Accept json
// @Produce json
// @Param config body models.SanitizationConfig true "Configuration to import"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/sanitization/rules/import [post]
func (h *RulesHandler) ImportRules(c *gin.Context) {
	var config models.SanitizationConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid configuration",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	if err := h.ruleService.ImportConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to import configuration",
			Message:   err.Error(),
			Timestamp: time.Now().Unix(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "Configuration imported successfully",
		Timestamp: time.Now().Unix(),
	})
}

// Response types
type ErrorResponse struct {
	Error     string `json:"error"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type SuccessResponse struct {
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
	RuleCount int    `json:"ruleCount"`
}

type MetricsResponse struct {
	TotalRules      int            `json:"totalRules"`
	EnabledRules    int            `json:"enabledRules"`
	DisabledRules   int            `json:"disabledRules"`
	RulesByType     map[string]int `json:"rulesByType"`
	RulesBySeverity map[string]int `json:"rulesBySeverity"`
	ConfigVersion   string         `json:"configVersion"`
	LastUpdated     int64          `json:"lastUpdated"`
}

type ToggleRequest struct {
	Enabled bool `json:"enabled"`
}

type ToggleResponse struct {
	Enabled   bool   `json:"enabled"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type RuleResponse struct {
	Rule      models.SanitizationRule `json:"rule"`
	Message   string                  `json:"message"`
	Timestamp int64                   `json:"timestamp"`
}

type BatchOperationRequest struct {
	RuleIDs   []string `json:"ruleIds"`
	Operation string   `json:"operation"` // "enable", "disable", "delete"
}

type BatchOperationResponse struct {
	SuccessCount int      `json:"successCount"`
	FailedCount  int      `json:"failedCount"`
	FailedRules  []string `json:"failedRules,omitempty"`
	Message      string   `json:"message"`
	Timestamp    int64    `json:"timestamp"`
}

type ValidateRuleRequest struct {
	Rule      models.SanitizationRule `json:"rule"`
	TestInput string                  `json:"testInput,omitempty"`
}

type ValidateRuleResponse struct {
	Valid      bool     `json:"valid"`
	Errors     []string `json:"errors,omitempty"`
	TestOutput string   `json:"testOutput,omitempty"`
	Message    string   `json:"message"`
	Timestamp  int64    `json:"timestamp"`
}
