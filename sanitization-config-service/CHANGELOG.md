# 变更日志

本文档记录了脱敏配置服务的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.0] - 2025-01-18

### 新增
- 🎉 初始版本发布
- ✨ 基于React 19.1.0的现代化前端应用
- 🔧 完整的脱敏规则CRUD功能
- 💾 基于LocalStorage的本地数据存储
- 🎨 现代化的用户界面设计
- 📱 响应式布局，支持多种屏幕尺寸
- 🔍 实时搜索和筛选功能
- 📊 规则统计信息展示
- 🔄 批量操作支持（启用、禁用、删除）
- 📤 配置文件导入导出功能
- 🎯 全局脱敏开关控制
- ⚡ 规则验证和测试功能

### 规则类型支持
- 📝 字段名匹配 (FIELD_NAME)
- 🔤 正则表达式匹配 (PATTERN)
- 📄 内容类型匹配 (CONTENT_TYPE)
- 🛠️ 自定义匹配逻辑 (CUSTOM)

### 严重程度分级
- 🔴 关键 (CRITICAL)
- 🟠 高 (HIGH)
- 🟡 中等 (MEDIUM)
- 🟢 低 (LOW)

### 预置默认规则
- 📱 手机号脱敏规则
- 📧 邮箱地址脱敏规则
- 🆔 身份证号脱敏规则

### 技术特性
- 🚀 纯前端实现，无需后端服务
- 🔒 数据安全，本地存储
- 📦 TypeScript类型安全
- 🎨 自定义CSS样式系统
- 🔧 模块化架构设计
- 📚 完整的文档体系

### 文档
- 📖 用户使用指南
- 🔧 开发文档
- 🚀 部署指南
- 📡 API文档
- 📊 项目总览

### 部署支持
- 🌐 静态文件服务器部署
- ☁️ 云平台部署（GitHub Pages、Netlify、Vercel等）
- 🐳 Docker容器化部署
- 📋 详细的部署指南

### 开发工具
- 🛠️ 开发环境启动脚本
- 📦 优化的构建配置
- 🧪 测试框架集成
- 📝 代码规范和类型检查

## [未来版本计划]

### 计划新增功能
- 🌍 国际化支持（多语言）
- 🔐 用户权限管理
- 📝 操作审计日志
- 🏷️ 规则标签和分类
- 📋 规则模板功能
- 🔄 规则版本管理
- 📊 使用统计和分析
- 🔗 后端API集成选项
- 💾 云端数据同步
- 📱 PWA支持

### 技术改进计划
- ⚡ 性能优化
- 🎨 UI/UX改进
- 🧪 测试覆盖率提升
- 📚 文档完善
- 🔧 开发工具改进

---

## 版本说明

### 版本号格式
采用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复

### 发布周期
- **主版本**：根据重大功能更新发布
- **次版本**：每月发布，包含新功能和改进
- **修订版本**：根据需要发布，主要用于bug修复

### 支持政策
- 当前主版本：完全支持
- 前一个主版本：安全更新和关键bug修复
- 更早版本：不再维护

---

*更多信息请参考项目文档和GitHub仓库。*
