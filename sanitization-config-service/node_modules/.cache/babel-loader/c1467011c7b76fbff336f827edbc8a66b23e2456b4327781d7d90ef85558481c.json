{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"ellipse\", {\n  cx: \"12\",\n  cy: \"5\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"msslwz\"\n}], [\"path\", {\n  d: \"M3 12a9 3 0 0 0 5 2.69\",\n  key: \"1ui2ym\"\n}], [\"path\", {\n  d: \"M21 9.3V5\",\n  key: \"6k6cib\"\n}], [\"path\", {\n  d: \"M3 5v14a9 3 0 0 0 6.47 2.88\",\n  key: \"i62tjy\"\n}], [\"path\", {\n  d: \"M12 12v4h4\",\n  key: \"1bxaet\"\n}], [\"path\", {\n  d: \"M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16\",\n  key: \"1f4ei9\"\n}]];\nconst DatabaseBackup = createLucideIcon(\"database-backup\", __iconNode);\nexport { __iconNode, DatabaseBackup as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "rx", "ry", "key", "d", "DatabaseBackup", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/database-backup.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 12a9 3 0 0 0 5 2.69', key: '1ui2ym' }],\n  ['path', { d: 'M21 9.3V5', key: '6k6cib' }],\n  ['path', { d: 'M3 5v14a9 3 0 0 0 6.47 2.88', key: 'i62tjy' }],\n  ['path', { d: 'M12 12v4h4', key: '1bxaet' }],\n  [\n    'path',\n    {\n      d: 'M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16',\n      key: '1f4ei9',\n    },\n  ],\n];\n\n/**\n * @component @name DatabaseBackup\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgMTJhOSAzIDAgMCAwIDUgMi42OSIgLz4KICA8cGF0aCBkPSJNMjEgOS4zVjUiIC8+CiAgPHBhdGggZD0iTTMgNXYxNGE5IDMgMCAwIDAgNi40NyAyLjg4IiAvPgogIDxwYXRoIGQ9Ik0xMiAxMnY0aDQiIC8+CiAgPHBhdGggZD0iTTEzIDIwYTUgNSAwIDAgMCA5LTMgNC41IDQuNSAwIDAgMC00LjUtNC41Yy0xLjMzIDAtMi41NC41NC0zLjQxIDEuNDFMMTIgMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database-backup\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DatabaseBackup = createLucideIcon('database-backup', __iconNode);\n\nexport default DatabaseBackup;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,WAAW;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAE,cAAA,GAAiBC,gBAAiB,oBAAmBR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}