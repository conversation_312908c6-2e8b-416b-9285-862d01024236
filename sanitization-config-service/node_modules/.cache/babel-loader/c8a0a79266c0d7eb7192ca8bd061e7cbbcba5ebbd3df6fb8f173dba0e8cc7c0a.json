{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 7h2a5 5 0 0 1 0 10h-2m-6 0H7A5 5 0 0 1 7 7h2\",\n  key: \"1re2ne\"\n}]];\nconst Unlink2 = createLucideIcon(\"unlink-2\", __iconNode);\nexport { __iconNode, Unlink2 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Unlink2", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/unlink-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 7h2a5 5 0 0 1 0 10h-2m-6 0H7A5 5 0 0 1 7 7h2', key: '1re2ne' }],\n];\n\n/**\n * @component @name Unlink2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgN2gyYTUgNSAwIDAgMSAwIDEwaC0ybS02IDBIN0E1IDUgMCAwIDEgNyA3aDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/unlink-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Unlink2 = createLucideIcon('unlink-2', __iconNode);\n\nexport default Unlink2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAU,GACnF;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}