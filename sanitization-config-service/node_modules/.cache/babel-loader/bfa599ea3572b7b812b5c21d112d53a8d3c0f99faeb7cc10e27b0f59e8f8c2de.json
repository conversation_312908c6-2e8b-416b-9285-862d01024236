{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18h4\",\n  key: \"1ulq68\"\n}], [\"path\", {\n  d: \"M11 6H3\",\n  key: \"1u26ik\"\n}], [\"path\", {\n  d: \"M15 6h6\",\n  key: \"1jlkvy\"\n}], [\"path\", {\n  d: \"M18 9V3\",\n  key: \"xwwp7m\"\n}], [\"path\", {\n  d: \"M7 12h8\",\n  key: \"7a1bxv\"\n}]];\nconst ListFilterPlus = createLucideIcon(\"list-filter-plus\", __iconNode);\nexport { __iconNode, ListFilterPlus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ListFilterPlus", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/list-filter-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 18h4', key: '1ulq68' }],\n  ['path', { d: 'M11 6H3', key: '1u26ik' }],\n  ['path', { d: 'M15 6h6', key: '1jlkvy' }],\n  ['path', { d: 'M18 9V3', key: 'xwwp7m' }],\n  ['path', { d: 'M7 12h8', key: '7a1bxv' }],\n];\n\n/**\n * @component @name ListFilterPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMThoNCIgLz4KICA8cGF0aCBkPSJNMTEgNkgzIiAvPgogIDxwYXRoIGQ9Ik0xNSA2aDYiIC8+CiAgPHBhdGggZD0iTTE4IDlWMyIgLz4KICA8cGF0aCBkPSJNNyAxMmg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/list-filter-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListFilterPlus = createLucideIcon('list-filter-plus', __iconNode);\n\nexport default ListFilterPlus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,qBAAoBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}