{"ast": null, "code": "// Local storage keys\nconst STORAGE_KEYS = {\n  RULES: 'sanitization_rules',\n  CONFIG: 'sanitization_config',\n  GLOBAL_ENABLED: 'sanitization_global_enabled'\n};\n\n// Default configuration\nconst DEFAULT_CONFIG = {\n  enabled: true,\n  rules: [{\n    id: 'phone-rule',\n    name: '手机号脱敏',\n    description: '对手机号进行脱敏处理，保留前3位和后4位',\n    type: 'PATTERN',\n    pattern: '1[3-9]\\\\d{9}',\n    fieldNames: ['phone', 'mobile', 'phoneNumber'],\n    maskValue: '***-****-****',\n    severity: 'HIGH',\n    enabled: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }, {\n    id: 'email-rule',\n    name: '邮箱脱敏',\n    description: '对邮箱地址进行脱敏处理',\n    type: 'PATTERN',\n    pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}',\n    fieldNames: ['email', 'emailAddress'],\n    maskValue: '***@***.***',\n    severity: 'MEDIUM',\n    enabled: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }, {\n    id: 'idcard-rule',\n    name: '身份证脱敏',\n    description: '对身份证号进行脱敏处理',\n    type: 'PATTERN',\n    pattern: '[1-9]\\\\d{5}(18|19|20)\\\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\\\d{3}[0-9Xx]',\n    fieldNames: ['idCard', 'identityCard', 'id'],\n    maskValue: '****-****-****-****',\n    severity: 'CRITICAL',\n    enabled: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }]\n};\n\n// Utility functions for local storage\nconst getStoredData = (key, defaultValue) => {\n  try {\n    const stored = localStorage.getItem(key);\n    return stored ? JSON.parse(stored) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage key ${key}:`, error);\n    return defaultValue;\n  }\n};\nconst setStoredData = (key, data) => {\n  try {\n    localStorage.setItem(key, JSON.stringify(data));\n  } catch (error) {\n    console.error(`Error writing to localStorage key ${key}:`, error);\n  }\n};\n\n// Initialize default data if not exists\nconst initializeDefaultData = () => {\n  if (!localStorage.getItem(STORAGE_KEYS.CONFIG)) {\n    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.GLOBAL_ENABLED)) {\n    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);\n  }\n};\n\n// Initialize on module load\ninitializeDefaultData();\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async serviceName => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const globalEnabled = getStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);\n    return {\n      ...config,\n      enabled: globalEnabled\n    };\n  },\n  // Create a new rule\n  createRule: async rule => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const newRule = {\n      ...rule,\n      id: rule.id || `rule-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    const updatedConfig = {\n      ...config,\n      rules: [...config.rules, newRule]\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n    return {\n      success: true,\n      data: newRule,\n      message: 'Rule created successfully'\n    };\n  },\n  // Update an existing rule\n  updateRule: async (ruleId, rule) => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);\n    if (ruleIndex === -1) {\n      throw new Error('Rule not found');\n    }\n    const updatedRule = {\n      ...rule,\n      id: ruleId,\n      createdAt: config.rules[ruleIndex].createdAt,\n      updatedAt: new Date().toISOString()\n    };\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n    return {\n      success: true,\n      data: updatedRule,\n      message: 'Rule updated successfully'\n    };\n  },\n  // Delete a rule\n  deleteRule: async ruleId => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.filter(r => r.id !== ruleId)\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n    return {\n      success: true,\n      data: null,\n      message: 'Rule deleted successfully'\n    };\n  },\n  // Toggle a specific rule's enabled status\n  toggleRule: async (ruleId, enabled) => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);\n    if (ruleIndex === -1) {\n      throw new Error('Rule not found');\n    }\n    const updatedRule = {\n      ...config.rules[ruleIndex],\n      enabled,\n      updatedAt: new Date().toISOString()\n    };\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n    return {\n      success: true,\n      data: updatedRule,\n      message: `Rule ${enabled ? 'enabled' : 'disabled'} successfully`\n    };\n  },\n  // Batch operations on multiple rules\n  batchOperation: async (ruleIds, operation) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    let updatedRules = [...config.rules];\n    let successCount = 0;\n    let failureCount = 0;\n    for (const ruleId of ruleIds) {\n      const ruleIndex = updatedRules.findIndex(r => r.id === ruleId);\n      if (ruleIndex !== -1) {\n        if (operation === 'delete') {\n          updatedRules = updatedRules.filter(r => r.id !== ruleId);\n        } else {\n          updatedRules[ruleIndex] = {\n            ...updatedRules[ruleIndex],\n            enabled: operation === 'enable',\n            updatedAt: new Date().toISOString()\n          };\n        }\n        successCount++;\n      } else {\n        failureCount++;\n      }\n    }\n    const updatedConfig = {\n      ...config,\n      rules: updatedRules\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n    return {\n      success: failureCount === 0,\n      successCount,\n      failureCount,\n      message: `Batch operation completed: ${successCount} successful, ${failureCount} failed`\n    };\n  },\n  // Validate a rule configuration\n  validateRule: async (rule, testInput) => {\n    var _rule$name, _rule$pattern, _rule$maskValue;\n    await new Promise(resolve => setTimeout(resolve, 100));\n    const errors = [];\n\n    // Basic validation\n    if (!((_rule$name = rule.name) !== null && _rule$name !== void 0 && _rule$name.trim())) {\n      errors.push('Rule name is required');\n    }\n    if (!rule.type) {\n      errors.push('Rule type is required');\n    }\n    if (rule.type === 'PATTERN' && !((_rule$pattern = rule.pattern) !== null && _rule$pattern !== void 0 && _rule$pattern.trim())) {\n      errors.push('Pattern is required for PATTERN type rules');\n    }\n    if (rule.type === 'FIELD_NAME' && (!rule.fieldNames || rule.fieldNames.length === 0)) {\n      errors.push('Field names are required for FIELD_NAME type rules');\n    }\n    if (!((_rule$maskValue = rule.maskValue) !== null && _rule$maskValue !== void 0 && _rule$maskValue.trim())) {\n      errors.push('Mask value is required');\n    }\n\n    // Pattern validation\n    if (rule.type === 'PATTERN' && rule.pattern) {\n      try {\n        new RegExp(rule.pattern);\n      } catch (e) {\n        errors.push('Invalid regular expression pattern');\n      }\n    }\n\n    // Test input validation if provided\n    let testResult = null;\n    if (testInput && rule.pattern && errors.length === 0) {\n      try {\n        const regex = new RegExp(rule.pattern, 'g');\n        const matches = testInput.match(regex);\n        testResult = {\n          input: testInput,\n          matches: matches || [],\n          masked: matches ? testInput.replace(regex, rule.maskValue) : testInput\n        };\n      } catch (e) {\n        errors.push('Error testing pattern against input');\n      }\n    }\n    return {\n      valid: errors.length === 0,\n      errors,\n      testResult,\n      message: errors.length === 0 ? 'Validation successful' : 'Validation failed',\n      timestamp: Date.now(),\n      testOutput: testResult ? testResult.masked : undefined\n    };\n  },\n  // Reload rules from configuration file\n  reloadRules: async () => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    // In a real frontend-only app, this would reset to default configuration\n    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    return {\n      success: true,\n      data: null,\n      message: 'Rules reloaded to default configuration'\n    };\n  },\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async enabled => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, enabled);\n    return {\n      enabled,\n      message: `Global sanitization ${enabled ? 'enabled' : 'disabled'}`,\n      timestamp: Date.now()\n    };\n  },\n  // Get service health\n  getHealth: async () => {\n    await new Promise(resolve => setTimeout(resolve, 50));\n    return {\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      version: '1.0.0',\n      uptime: Date.now(),\n      checks: {\n        storage: 'healthy',\n        memory: 'healthy'\n      }\n    };\n  },\n  // Get service metrics\n  getMetrics: async () => {\n    await new Promise(resolve => setTimeout(resolve, 50));\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    return {\n      totalRules: config.rules.length,\n      enabledRules: config.rules.filter(r => r.enabled).length,\n      disabledRules: config.rules.filter(r => !r.enabled).length,\n      rulesByType: {\n        FIELD_NAME: config.rules.filter(r => r.type === 'FIELD_NAME').length,\n        PATTERN: config.rules.filter(r => r.type === 'PATTERN').length,\n        CONTENT_TYPE: config.rules.filter(r => r.type === 'CONTENT_TYPE').length,\n        CUSTOM: config.rules.filter(r => r.type === 'CUSTOM').length\n      },\n      rulesBySeverity: {\n        CRITICAL: config.rules.filter(r => r.severity === 'CRITICAL').length,\n        HIGH: config.rules.filter(r => r.severity === 'HIGH').length,\n        MEDIUM: config.rules.filter(r => r.severity === 'MEDIUM').length,\n        LOW: config.rules.filter(r => r.severity === 'LOW').length\n      }\n    };\n  },\n  // Export rules configuration\n  exportRules: async () => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n    return getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n  },\n  // Import rules configuration\n  importRules: async config => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n\n    // Validate imported configuration\n    if (!config || !Array.isArray(config.rules)) {\n      throw new Error('Invalid configuration format');\n    }\n\n    // Add timestamps to imported rules\n    const processedConfig = {\n      ...config,\n      rules: config.rules.map(rule => ({\n        ...rule,\n        createdAt: rule.createdAt || new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }))\n    };\n    setStoredData(STORAGE_KEYS.CONFIG, processedConfig);\n    return {\n      success: true,\n      data: processedConfig,\n      message: `Successfully imported ${config.rules.length} rules`\n    };\n  }\n};", "map": {"version": 3, "names": ["STORAGE_KEYS", "RULES", "CONFIG", "GLOBAL_ENABLED", "DEFAULT_CONFIG", "enabled", "rules", "id", "name", "description", "type", "pattern", "fieldNames", "maskValue", "severity", "createdAt", "Date", "toISOString", "updatedAt", "getStoredData", "key", "defaultValue", "stored", "localStorage", "getItem", "JSON", "parse", "error", "console", "setStoredData", "data", "setItem", "stringify", "initializeDefaultData", "sanitizationApi", "getRules", "serviceName", "Promise", "resolve", "setTimeout", "config", "globalEnabled", "createRule", "rule", "newRule", "now", "updatedConfig", "success", "message", "updateRule", "ruleId", "ruleIndex", "findIndex", "r", "Error", "updatedRule", "map", "index", "deleteRule", "filter", "toggleRule", "batchOperation", "ruleIds", "operation", "updatedRules", "successCount", "failureCount", "validateRule", "testInput", "_rule$name", "_rule$pattern", "_rule$maskValue", "errors", "trim", "push", "length", "RegExp", "e", "testResult", "regex", "matches", "match", "input", "masked", "replace", "valid", "timestamp", "testOutput", "undefined", "reloadRules", "toggleGlobalSwitch", "getHealth", "status", "version", "uptime", "checks", "storage", "memory", "getMetrics", "totalRules", "enabledRules", "disabledRules", "rulesByType", "FIELD_NAME", "PATTERN", "CONTENT_TYPE", "CUSTOM", "rulesBySeverity", "CRITICAL", "HIGH", "MEDIUM", "LOW", "exportRules", "importRules", "Array", "isArray", "processedConfig"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts"], "sourcesContent": ["import {\n  SanitizationConfig,\n  SanitizationRule,\n  HealthResponse,\n  MetricsResponse,\n  ApiResponse,\n  BatchOperationResponse,\n  ValidationResponse\n} from '../types';\n\n// Local storage keys\nconst STORAGE_KEYS = {\n  RULES: 'sanitization_rules',\n  CONFIG: 'sanitization_config',\n  GLOBAL_ENABLED: 'sanitization_global_enabled'\n};\n\n// Default configuration\nconst DEFAULT_CONFIG: SanitizationConfig = {\n  enabled: true,\n  rules: [\n    {\n      id: 'phone-rule',\n      name: '手机号脱敏',\n      description: '对手机号进行脱敏处理，保留前3位和后4位',\n      type: 'PATTERN',\n      pattern: '1[3-9]\\\\d{9}',\n      fieldNames: ['phone', 'mobile', 'phoneNumber'],\n      maskValue: '***-****-****',\n      severity: 'HIGH',\n      enabled: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    },\n    {\n      id: 'email-rule',\n      name: '邮箱脱敏',\n      description: '对邮箱地址进行脱敏处理',\n      type: 'PATTERN',\n      pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}',\n      fieldNames: ['email', 'emailAddress'],\n      maskValue: '***@***.***',\n      severity: 'MEDIUM',\n      enabled: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    },\n    {\n      id: 'idcard-rule',\n      name: '身份证脱敏',\n      description: '对身份证号进行脱敏处理',\n      type: 'PATTERN',\n      pattern: '[1-9]\\\\d{5}(18|19|20)\\\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\\\d{3}[0-9Xx]',\n      fieldNames: ['idCard', 'identityCard', 'id'],\n      maskValue: '****-****-****-****',\n      severity: 'CRITICAL',\n      enabled: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n  ]\n};\n\n// Utility functions for local storage\nconst getStoredData = <T>(key: string, defaultValue: T): T => {\n  try {\n    const stored = localStorage.getItem(key);\n    return stored ? JSON.parse(stored) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage key ${key}:`, error);\n    return defaultValue;\n  }\n};\n\nconst setStoredData = <T>(key: string, data: T): void => {\n  try {\n    localStorage.setItem(key, JSON.stringify(data));\n  } catch (error) {\n    console.error(`Error writing to localStorage key ${key}:`, error);\n  }\n};\n\n// Initialize default data if not exists\nconst initializeDefaultData = () => {\n  if (!localStorage.getItem(STORAGE_KEYS.CONFIG)) {\n    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.GLOBAL_ENABLED)) {\n    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);\n  }\n};\n\n// Initialize on module load\ninitializeDefaultData();\n\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async (serviceName?: string): Promise<SanitizationConfig> => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const globalEnabled = getStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);\n\n    return {\n      ...config,\n      enabled: globalEnabled\n    };\n  },\n\n  // Create a new rule\n  createRule: async (rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const newRule: SanitizationRule = {\n      ...rule,\n      id: rule.id || `rule-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    const updatedConfig = {\n      ...config,\n      rules: [...config.rules, newRule]\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n\n    return {\n      success: true,\n      data: newRule,\n      message: 'Rule created successfully'\n    };\n  },\n\n  // Update an existing rule\n  updateRule: async (ruleId: string, rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);\n\n    if (ruleIndex === -1) {\n      throw new Error('Rule not found');\n    }\n\n    const updatedRule: SanitizationRule = {\n      ...rule,\n      id: ruleId,\n      createdAt: config.rules[ruleIndex].createdAt,\n      updatedAt: new Date().toISOString()\n    };\n\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n\n    return {\n      success: true,\n      data: updatedRule,\n      message: 'Rule updated successfully'\n    };\n  },\n\n  // Delete a rule\n  deleteRule: async (ruleId: string): Promise<ApiResponse<any>> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.filter(r => r.id !== ruleId)\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n\n    return {\n      success: true,\n      data: null,\n      message: 'Rule deleted successfully'\n    };\n  },\n\n  // Toggle a specific rule's enabled status\n  toggleRule: async (ruleId: string, enabled: boolean): Promise<ApiResponse<SanitizationRule>> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);\n\n    if (ruleIndex === -1) {\n      throw new Error('Rule not found');\n    }\n\n    const updatedRule = {\n      ...config.rules[ruleIndex],\n      enabled,\n      updatedAt: new Date().toISOString()\n    };\n\n    const updatedConfig = {\n      ...config,\n      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n\n    return {\n      success: true,\n      data: updatedRule,\n      message: `Rule ${enabled ? 'enabled' : 'disabled'} successfully`\n    };\n  },\n\n  // Batch operations on multiple rules\n  batchOperation: async (ruleIds: string[], operation: 'enable' | 'disable' | 'delete'): Promise<BatchOperationResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n    let updatedRules = [...config.rules];\n    let successCount = 0;\n    let failureCount = 0;\n\n    for (const ruleId of ruleIds) {\n      const ruleIndex = updatedRules.findIndex(r => r.id === ruleId);\n      if (ruleIndex !== -1) {\n        if (operation === 'delete') {\n          updatedRules = updatedRules.filter(r => r.id !== ruleId);\n        } else {\n          updatedRules[ruleIndex] = {\n            ...updatedRules[ruleIndex],\n            enabled: operation === 'enable',\n            updatedAt: new Date().toISOString()\n          };\n        }\n        successCount++;\n      } else {\n        failureCount++;\n      }\n    }\n\n    const updatedConfig = {\n      ...config,\n      rules: updatedRules\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);\n\n    return {\n      success: failureCount === 0,\n      successCount,\n      failureCount,\n      message: `Batch operation completed: ${successCount} successful, ${failureCount} failed`\n    };\n  },\n\n  // Validate a rule configuration\n  validateRule: async (rule: SanitizationRule, testInput?: string): Promise<ValidationResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const errors: string[] = [];\n\n    // Basic validation\n    if (!rule.name?.trim()) {\n      errors.push('Rule name is required');\n    }\n\n    if (!rule.type) {\n      errors.push('Rule type is required');\n    }\n\n    if (rule.type === 'PATTERN' && !rule.pattern?.trim()) {\n      errors.push('Pattern is required for PATTERN type rules');\n    }\n\n    if (rule.type === 'FIELD_NAME' && (!rule.fieldNames || rule.fieldNames.length === 0)) {\n      errors.push('Field names are required for FIELD_NAME type rules');\n    }\n\n    if (!rule.maskValue?.trim()) {\n      errors.push('Mask value is required');\n    }\n\n    // Pattern validation\n    if (rule.type === 'PATTERN' && rule.pattern) {\n      try {\n        new RegExp(rule.pattern);\n      } catch (e) {\n        errors.push('Invalid regular expression pattern');\n      }\n    }\n\n    // Test input validation if provided\n    let testResult = null;\n    if (testInput && rule.pattern && errors.length === 0) {\n      try {\n        const regex = new RegExp(rule.pattern, 'g');\n        const matches = testInput.match(regex);\n        testResult = {\n          input: testInput,\n          matches: matches || [],\n          masked: matches ? testInput.replace(regex, rule.maskValue) : testInput\n        };\n      } catch (e) {\n        errors.push('Error testing pattern against input');\n      }\n    }\n\n    return {\n      valid: errors.length === 0,\n      errors,\n      testResult,\n      message: errors.length === 0 ? 'Validation successful' : 'Validation failed',\n      timestamp: Date.now(),\n      testOutput: testResult ? testResult.masked : undefined\n    };\n  },\n\n  // Reload rules from configuration file\n  reloadRules: async (): Promise<ApiResponse<any>> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    // In a real frontend-only app, this would reset to default configuration\n    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n\n    return {\n      success: true,\n      data: null,\n      message: 'Rules reloaded to default configuration'\n    };\n  },\n\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async (enabled: boolean): Promise<{ enabled: boolean; message: string; timestamp: number }> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, enabled);\n\n    return {\n      enabled,\n      message: `Global sanitization ${enabled ? 'enabled' : 'disabled'}`,\n      timestamp: Date.now()\n    };\n  },\n\n  // Get service health\n  getHealth: async (): Promise<HealthResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 50));\n\n    return {\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      version: '1.0.0',\n      uptime: Date.now(),\n      checks: {\n        storage: 'healthy',\n        memory: 'healthy'\n      }\n    };\n  },\n\n  // Get service metrics\n  getMetrics: async (): Promise<MetricsResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 50));\n\n    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n\n    return {\n      totalRules: config.rules.length,\n      enabledRules: config.rules.filter(r => r.enabled).length,\n      disabledRules: config.rules.filter(r => !r.enabled).length,\n      rulesByType: {\n        FIELD_NAME: config.rules.filter(r => r.type === 'FIELD_NAME').length,\n        PATTERN: config.rules.filter(r => r.type === 'PATTERN').length,\n        CONTENT_TYPE: config.rules.filter(r => r.type === 'CONTENT_TYPE').length,\n        CUSTOM: config.rules.filter(r => r.type === 'CUSTOM').length\n      },\n      rulesBySeverity: {\n        CRITICAL: config.rules.filter(r => r.severity === 'CRITICAL').length,\n        HIGH: config.rules.filter(r => r.severity === 'HIGH').length,\n        MEDIUM: config.rules.filter(r => r.severity === 'MEDIUM').length,\n        LOW: config.rules.filter(r => r.severity === 'LOW').length\n      }\n    };\n  },\n\n  // Export rules configuration\n  exportRules: async (): Promise<SanitizationConfig> => {\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    return getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);\n  },\n\n  // Import rules configuration\n  importRules: async (config: SanitizationConfig): Promise<ApiResponse<any>> => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n\n    // Validate imported configuration\n    if (!config || !Array.isArray(config.rules)) {\n      throw new Error('Invalid configuration format');\n    }\n\n    // Add timestamps to imported rules\n    const processedConfig = {\n      ...config,\n      rules: config.rules.map(rule => ({\n        ...rule,\n        createdAt: rule.createdAt || new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }))\n    };\n\n    setStoredData(STORAGE_KEYS.CONFIG, processedConfig);\n\n    return {\n      success: true,\n      data: processedConfig,\n      message: `Successfully imported ${config.rules.length} rules`\n    };\n  },\n};\n"], "mappings": "AAUA;AACA,MAAMA,YAAY,GAAG;EACnBC,KAAK,EAAE,oBAAoB;EAC3BC,MAAM,EAAE,qBAAqB;EAC7BC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,MAAMC,cAAkC,GAAG;EACzCC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,CACL;IACEC,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,cAAc;IACvBC,UAAU,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC9CC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,MAAM;IAChBT,OAAO,EAAE,IAAI;IACbU,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC,EACD;IACEV,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,aAAa;IAC1BC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,iDAAiD;IAC1DC,UAAU,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC;IACrCC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,QAAQ;IAClBT,OAAO,EAAE,IAAI;IACbU,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC,EACD;IACEV,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,aAAa;IAC1BC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,uFAAuF;IAChGC,UAAU,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC;IAC5CC,SAAS,EAAE,qBAAqB;IAChCC,QAAQ,EAAE,UAAU;IACpBT,OAAO,EAAE,IAAI;IACbU,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;AAEL,CAAC;;AAED;AACA,MAAME,aAAa,GAAGA,CAAIC,GAAW,EAAEC,YAAe,KAAQ;EAC5D,IAAI;IACF,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAACJ,GAAG,CAAC;IACxC,OAAOE,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC,GAAGD,YAAY;EACnD,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCP,GAAG,GAAG,EAAEO,KAAK,CAAC;IACnE,OAAON,YAAY;EACrB;AACF,CAAC;AAED,MAAMQ,aAAa,GAAGA,CAAIT,GAAW,EAAEU,IAAO,KAAW;EACvD,IAAI;IACFP,YAAY,CAACQ,OAAO,CAACX,GAAG,EAAEK,IAAI,CAACO,SAAS,CAACF,IAAI,CAAC,CAAC;EACjD,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,qCAAqCP,GAAG,GAAG,EAAEO,KAAK,CAAC;EACnE;AACF,CAAC;;AAED;AACA,MAAMM,qBAAqB,GAAGA,CAAA,KAAM;EAClC,IAAI,CAACV,YAAY,CAACC,OAAO,CAACxB,YAAY,CAACE,MAAM,CAAC,EAAE;IAC9C2B,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;EACpD;EACA,IAAI,CAACmB,YAAY,CAACC,OAAO,CAACxB,YAAY,CAACG,cAAc,CAAC,EAAE;IACtD0B,aAAa,CAAC7B,YAAY,CAACG,cAAc,EAAE,IAAI,CAAC;EAClD;AACF,CAAC;;AAED;AACA8B,qBAAqB,CAAC,CAAC;AAEvB,OAAO,MAAMC,eAAe,GAAG;EAC7B;EACAC,QAAQ,EAAE,MAAOC,WAAoB,IAAkC;IACrE;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,MAAMqC,aAAa,GAAGtB,aAAa,CAACnB,YAAY,CAACG,cAAc,EAAE,IAAI,CAAC;IAEtE,OAAO;MACL,GAAGqC,MAAM;MACTnC,OAAO,EAAEoC;IACX,CAAC;EACH,CAAC;EAED;EACAC,UAAU,EAAE,MAAOC,IAAsB,IAA6C;IACpF,MAAM,IAAIN,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,MAAMwC,OAAyB,GAAG;MAChC,GAAGD,IAAI;MACPpC,EAAE,EAAEoC,IAAI,CAACpC,EAAE,IAAI,QAAQS,IAAI,CAAC6B,GAAG,CAAC,CAAC,EAAE;MACnC9B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,MAAM6B,aAAa,GAAG;MACpB,GAAGN,MAAM;MACTlC,KAAK,EAAE,CAAC,GAAGkC,MAAM,CAAClC,KAAK,EAAEsC,OAAO;IAClC,CAAC;IAEDf,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAE4C,aAAa,CAAC;IAEjD,OAAO;MACLC,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAEc,OAAO;MACbI,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAC,UAAU,EAAE,MAAAA,CAAOC,MAAc,EAAEP,IAAsB,KAA6C;IACpG,MAAM,IAAIN,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,MAAM+C,SAAS,GAAGX,MAAM,CAAClC,KAAK,CAAC8C,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK2C,MAAM,CAAC;IAE9D,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAIG,KAAK,CAAC,gBAAgB,CAAC;IACnC;IAEA,MAAMC,WAA6B,GAAG;MACpC,GAAGZ,IAAI;MACPpC,EAAE,EAAE2C,MAAM;MACVnC,SAAS,EAAEyB,MAAM,CAAClC,KAAK,CAAC6C,SAAS,CAAC,CAACpC,SAAS;MAC5CG,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,MAAM6B,aAAa,GAAG;MACpB,GAAGN,MAAM;MACTlC,KAAK,EAAEkC,MAAM,CAAClC,KAAK,CAACkD,GAAG,CAAC,CAACH,CAAC,EAAEI,KAAK,KAAKA,KAAK,KAAKN,SAAS,GAAGI,WAAW,GAAGF,CAAC;IAC7E,CAAC;IAEDxB,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAE4C,aAAa,CAAC;IAEjD,OAAO;MACLC,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAEyB,WAAW;MACjBP,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAU,UAAU,EAAE,MAAOR,MAAc,IAAgC;IAC/D,MAAM,IAAIb,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,MAAM0C,aAAa,GAAG;MACpB,GAAGN,MAAM;MACTlC,KAAK,EAAEkC,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK2C,MAAM;IACjD,CAAC;IAEDrB,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAE4C,aAAa,CAAC;IAEjD,OAAO;MACLC,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAE,IAAI;MACVkB,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAY,UAAU,EAAE,MAAAA,CAAOV,MAAc,EAAE7C,OAAgB,KAA6C;IAC9F,MAAM,IAAIgC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,MAAM+C,SAAS,GAAGX,MAAM,CAAClC,KAAK,CAAC8C,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK2C,MAAM,CAAC;IAE9D,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAIG,KAAK,CAAC,gBAAgB,CAAC;IACnC;IAEA,MAAMC,WAAW,GAAG;MAClB,GAAGf,MAAM,CAAClC,KAAK,CAAC6C,SAAS,CAAC;MAC1B9C,OAAO;MACPa,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,MAAM6B,aAAa,GAAG;MACpB,GAAGN,MAAM;MACTlC,KAAK,EAAEkC,MAAM,CAAClC,KAAK,CAACkD,GAAG,CAAC,CAACH,CAAC,EAAEI,KAAK,KAAKA,KAAK,KAAKN,SAAS,GAAGI,WAAW,GAAGF,CAAC;IAC7E,CAAC;IAEDxB,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAE4C,aAAa,CAAC;IAEjD,OAAO;MACLC,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAEyB,WAAW;MACjBP,OAAO,EAAE,QAAQ3C,OAAO,GAAG,SAAS,GAAG,UAAU;IACnD,CAAC;EACH,CAAC;EAED;EACAwD,cAAc,EAAE,MAAAA,CAAOC,OAAiB,EAAEC,SAA0C,KAAsC;IACxH,MAAM,IAAI1B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IACjE,IAAI4D,YAAY,GAAG,CAAC,GAAGxB,MAAM,CAAClC,KAAK,CAAC;IACpC,IAAI2D,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAG,CAAC;IAEpB,KAAK,MAAMhB,MAAM,IAAIY,OAAO,EAAE;MAC5B,MAAMX,SAAS,GAAGa,YAAY,CAACZ,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK2C,MAAM,CAAC;MAC9D,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB,IAAIY,SAAS,KAAK,QAAQ,EAAE;UAC1BC,YAAY,GAAGA,YAAY,CAACL,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK2C,MAAM,CAAC;QAC1D,CAAC,MAAM;UACLc,YAAY,CAACb,SAAS,CAAC,GAAG;YACxB,GAAGa,YAAY,CAACb,SAAS,CAAC;YAC1B9C,OAAO,EAAE0D,SAAS,KAAK,QAAQ;YAC/B7C,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC;QACH;QACAgD,YAAY,EAAE;MAChB,CAAC,MAAM;QACLC,YAAY,EAAE;MAChB;IACF;IAEA,MAAMpB,aAAa,GAAG;MACpB,GAAGN,MAAM;MACTlC,KAAK,EAAE0D;IACT,CAAC;IAEDnC,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAE4C,aAAa,CAAC;IAEjD,OAAO;MACLC,OAAO,EAAEmB,YAAY,KAAK,CAAC;MAC3BD,YAAY;MACZC,YAAY;MACZlB,OAAO,EAAE,8BAA8BiB,YAAY,gBAAgBC,YAAY;IACjF,CAAC;EACH,CAAC;EAED;EACAC,YAAY,EAAE,MAAAA,CAAOxB,IAAsB,EAAEyB,SAAkB,KAAkC;IAAA,IAAAC,UAAA,EAAAC,aAAA,EAAAC,eAAA;IAC/F,MAAM,IAAIlC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMkC,MAAgB,GAAG,EAAE;;IAE3B;IACA,IAAI,GAAAH,UAAA,GAAC1B,IAAI,CAACnC,IAAI,cAAA6D,UAAA,eAATA,UAAA,CAAWI,IAAI,CAAC,CAAC,GAAE;MACtBD,MAAM,CAACE,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAI,CAAC/B,IAAI,CAACjC,IAAI,EAAE;MACd8D,MAAM,CAACE,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAI/B,IAAI,CAACjC,IAAI,KAAK,SAAS,IAAI,GAAA4D,aAAA,GAAC3B,IAAI,CAAChC,OAAO,cAAA2D,aAAA,eAAZA,aAAA,CAAcG,IAAI,CAAC,CAAC,GAAE;MACpDD,MAAM,CAACE,IAAI,CAAC,4CAA4C,CAAC;IAC3D;IAEA,IAAI/B,IAAI,CAACjC,IAAI,KAAK,YAAY,KAAK,CAACiC,IAAI,CAAC/B,UAAU,IAAI+B,IAAI,CAAC/B,UAAU,CAAC+D,MAAM,KAAK,CAAC,CAAC,EAAE;MACpFH,MAAM,CAACE,IAAI,CAAC,oDAAoD,CAAC;IACnE;IAEA,IAAI,GAAAH,eAAA,GAAC5B,IAAI,CAAC9B,SAAS,cAAA0D,eAAA,eAAdA,eAAA,CAAgBE,IAAI,CAAC,CAAC,GAAE;MAC3BD,MAAM,CAACE,IAAI,CAAC,wBAAwB,CAAC;IACvC;;IAEA;IACA,IAAI/B,IAAI,CAACjC,IAAI,KAAK,SAAS,IAAIiC,IAAI,CAAChC,OAAO,EAAE;MAC3C,IAAI;QACF,IAAIiE,MAAM,CAACjC,IAAI,CAAChC,OAAO,CAAC;MAC1B,CAAC,CAAC,OAAOkE,CAAC,EAAE;QACVL,MAAM,CAACE,IAAI,CAAC,oCAAoC,CAAC;MACnD;IACF;;IAEA;IACA,IAAII,UAAU,GAAG,IAAI;IACrB,IAAIV,SAAS,IAAIzB,IAAI,CAAChC,OAAO,IAAI6D,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI;QACF,MAAMI,KAAK,GAAG,IAAIH,MAAM,CAACjC,IAAI,CAAChC,OAAO,EAAE,GAAG,CAAC;QAC3C,MAAMqE,OAAO,GAAGZ,SAAS,CAACa,KAAK,CAACF,KAAK,CAAC;QACtCD,UAAU,GAAG;UACXI,KAAK,EAAEd,SAAS;UAChBY,OAAO,EAAEA,OAAO,IAAI,EAAE;UACtBG,MAAM,EAAEH,OAAO,GAAGZ,SAAS,CAACgB,OAAO,CAACL,KAAK,EAAEpC,IAAI,CAAC9B,SAAS,CAAC,GAAGuD;QAC/D,CAAC;MACH,CAAC,CAAC,OAAOS,CAAC,EAAE;QACVL,MAAM,CAACE,IAAI,CAAC,qCAAqC,CAAC;MACpD;IACF;IAEA,OAAO;MACLW,KAAK,EAAEb,MAAM,CAACG,MAAM,KAAK,CAAC;MAC1BH,MAAM;MACNM,UAAU;MACV9B,OAAO,EAAEwB,MAAM,CAACG,MAAM,KAAK,CAAC,GAAG,uBAAuB,GAAG,mBAAmB;MAC5EW,SAAS,EAAEtE,IAAI,CAAC6B,GAAG,CAAC,CAAC;MACrB0C,UAAU,EAAET,UAAU,GAAGA,UAAU,CAACK,MAAM,GAAGK;IAC/C,CAAC;EACH,CAAC;EAED;EACAC,WAAW,EAAE,MAAAA,CAAA,KAAuC;IAClD,MAAM,IAAIpD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACAT,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IAElD,OAAO;MACL2C,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAE,IAAI;MACVkB,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACA0C,kBAAkB,EAAE,MAAOrF,OAAgB,IAAwE;IACjH,MAAM,IAAIgC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtDT,aAAa,CAAC7B,YAAY,CAACG,cAAc,EAAEE,OAAO,CAAC;IAEnD,OAAO;MACLA,OAAO;MACP2C,OAAO,EAAE,uBAAuB3C,OAAO,GAAG,SAAS,GAAG,UAAU,EAAE;MAClEiF,SAAS,EAAEtE,IAAI,CAAC6B,GAAG,CAAC;IACtB,CAAC;EACH,CAAC;EAED;EACA8C,SAAS,EAAE,MAAAA,CAAA,KAAqC;IAC9C,MAAM,IAAItD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;IAErD,OAAO;MACLsD,MAAM,EAAE,SAAS;MACjBN,SAAS,EAAE,IAAItE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnC4E,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE9E,IAAI,CAAC6B,GAAG,CAAC,CAAC;MAClBkD,MAAM,EAAE;QACNC,OAAO,EAAE,SAAS;QAClBC,MAAM,EAAE;MACV;IACF,CAAC;EACH,CAAC;EAED;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAsC;IAChD,MAAM,IAAI7D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;IAErD,MAAME,MAAM,GAAGrB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;IAEjE,OAAO;MACL+F,UAAU,EAAE3D,MAAM,CAAClC,KAAK,CAACqE,MAAM;MAC/ByB,YAAY,EAAE5D,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAChD,OAAO,CAAC,CAACsE,MAAM;MACxD0B,aAAa,EAAE7D,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAI,CAACA,CAAC,CAAChD,OAAO,CAAC,CAACsE,MAAM;MAC1D2B,WAAW,EAAE;QACXC,UAAU,EAAE/D,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAK,YAAY,CAAC,CAACiE,MAAM;QACpE6B,OAAO,EAAEhE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAK,SAAS,CAAC,CAACiE,MAAM;QAC9D8B,YAAY,EAAEjE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAK,cAAc,CAAC,CAACiE,MAAM;QACxE+B,MAAM,EAAElE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAK,QAAQ,CAAC,CAACiE;MACxD,CAAC;MACDgC,eAAe,EAAE;QACfC,QAAQ,EAAEpE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACvC,QAAQ,KAAK,UAAU,CAAC,CAAC6D,MAAM;QACpEkC,IAAI,EAAErE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACvC,QAAQ,KAAK,MAAM,CAAC,CAAC6D,MAAM;QAC5DmC,MAAM,EAAEtE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACvC,QAAQ,KAAK,QAAQ,CAAC,CAAC6D,MAAM;QAChEoC,GAAG,EAAEvE,MAAM,CAAClC,KAAK,CAACqD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACvC,QAAQ,KAAK,KAAK,CAAC,CAAC6D;MACtD;IACF,CAAC;EACH,CAAC;EAED;EACAqC,WAAW,EAAE,MAAAA,CAAA,KAAyC;IACpD,MAAM,IAAI3E,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,OAAOnB,aAAa,CAACnB,YAAY,CAACE,MAAM,EAAEE,cAAc,CAAC;EAC3D,CAAC;EAED;EACA6G,WAAW,EAAE,MAAOzE,MAA0B,IAAgC;IAC5E,MAAM,IAAIH,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,IAAI,CAACE,MAAM,IAAI,CAAC0E,KAAK,CAACC,OAAO,CAAC3E,MAAM,CAAClC,KAAK,CAAC,EAAE;MAC3C,MAAM,IAAIgD,KAAK,CAAC,8BAA8B,CAAC;IACjD;;IAEA;IACA,MAAM8D,eAAe,GAAG;MACtB,GAAG5E,MAAM;MACTlC,KAAK,EAAEkC,MAAM,CAAClC,KAAK,CAACkD,GAAG,CAACb,IAAI,KAAK;QAC/B,GAAGA,IAAI;QACP5B,SAAS,EAAE4B,IAAI,CAAC5B,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrDC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC;IAEDY,aAAa,CAAC7B,YAAY,CAACE,MAAM,EAAEkH,eAAe,CAAC;IAEnD,OAAO;MACLrE,OAAO,EAAE,IAAI;MACbjB,IAAI,EAAEsF,eAAe;MACrBpE,OAAO,EAAE,yBAAyBR,MAAM,CAAClC,KAAK,CAACqE,MAAM;IACvD,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}