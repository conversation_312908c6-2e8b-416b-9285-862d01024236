# 项目总览

## 项目信息

- **项目名称**: 脱敏配置服务 (Sanitization Config Service)
- **版本**: 1.0.0
- **类型**: 纯前端Web应用
- **技术栈**: React + TypeScript
- **许可证**: MIT

## 项目描述

脱敏配置服务是一个基于React的纯前端数据脱敏规则管理系统。它提供了直观的用户界面，用于创建、编辑和管理各种数据脱敏规则，帮助保护敏感信息的安全。

### 核心特性

- 🎨 **现代化UI设计** - 简洁、直观的用户界面
- 📱 **纯前端实现** - 无需后端服务，部署简单
- 💾 **本地数据存储** - 使用浏览器LocalStorage
- 🔧 **规则管理** - 完整的CRUD操作
- 🔄 **批量操作** - 支持批量启用、禁用、删除
- 📤 **导入导出** - 配置文件的导入和导出
- 🔍 **搜索筛选** - 快速查找和过滤规则
- 🎯 **全局控制** - 一键启用/禁用所有脱敏功能

## 技术架构

### 前端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| React | 19.1.0 | 用户界面框架 |
| TypeScript | 4.9.5 | 类型安全的JavaScript |
| Lucide React | 0.525.0 | 现代化图标库 |
| React Hot Toast | 2.5.2 | 消息提示组件 |
| React Scripts | 5.0.1 | 构建工具 |

### 数据存储

- **LocalStorage** - 浏览器本地存储
- **JSON格式** - 结构化数据存储
- **实时同步** - 数据变更即时保存

### 项目结构

```
sanitization-config-service/
├── public/                 # 静态资源
│   ├── index.html         # HTML模板
│   ├── favicon.ico        # 网站图标
│   └── manifest.json      # PWA配置
├── src/                   # 源代码
│   ├── components/        # React组件（预留）
│   ├── services/         # API服务层
│   │   └── api.ts        # 本地存储API实现
│   ├── types/           # TypeScript类型定义
│   │   └── index.ts     # 主要类型定义
│   ├── utils/           # 工具函数（预留）
│   ├── App.tsx          # 主应用组件
│   ├── index.tsx        # 应用入口
│   └── index.css        # 全局样式
├── build/               # 构建输出目录
├── docs/               # 文档目录
│   ├── README.md       # 项目说明
│   ├── DEVELOPMENT.md  # 开发文档
│   ├── DEPLOYMENT.md   # 部署指南
│   ├── USER_GUIDE.md   # 用户指南
│   └── API.md          # API文档
├── package.json        # 项目配置
├── tsconfig.json       # TypeScript配置
└── start-frontend.sh   # 启动脚本
```

## 功能模块

### 1. 规则管理模块

- **规则列表** - 展示所有脱敏规则
- **规则创建** - 创建新的脱敏规则
- **规则编辑** - 修改现有规则
- **规则删除** - 删除不需要的规则
- **状态切换** - 启用/禁用规则

### 2. 搜索筛选模块

- **实时搜索** - 按名称、描述、ID搜索
- **统计信息** - 显示规则数量统计
- **快速过滤** - 按状态、类型筛选

### 3. 批量操作模块

- **批量选择** - 多选规则
- **批量启用** - 一键启用多个规则
- **批量禁用** - 一键禁用多个规则
- **批量删除** - 一键删除多个规则

### 4. 配置管理模块

- **全局开关** - 控制整体脱敏功能
- **配置导出** - 导出JSON配置文件
- **配置导入** - 导入JSON配置文件
- **配置验证** - 验证规则配置正确性

### 5. 数据存储模块

- **本地存储** - LocalStorage数据持久化
- **数据同步** - 实时保存变更
- **默认配置** - 预置常用脱敏规则

## 数据模型

### 规则类型

- **FIELD_NAME** - 字段名匹配
- **PATTERN** - 正则表达式匹配
- **CONTENT_TYPE** - 内容类型匹配
- **CUSTOM** - 自定义匹配逻辑

### 严重程度

- **CRITICAL** - 关键（红色标识）
- **HIGH** - 高（橙色标识）
- **MEDIUM** - 中等（黄色标识）
- **LOW** - 低（绿色标识）

### 默认规则

系统预置三个常用脱敏规则：

1. **手机号脱敏** - 匹配中国手机号格式
2. **邮箱脱敏** - 匹配邮箱地址格式
3. **身份证脱敏** - 匹配中国身份证号格式

## 开发流程

### 环境准备

1. 安装Node.js 16+
2. 克隆项目代码
3. 安装依赖：`npm install`
4. 启动开发服务器：`npm start`

### 构建部署

1. 构建生产版本：`npm run build`
2. 部署到静态文件服务器
3. 配置服务器支持SPA路由

### 测试验证

1. 运行单元测试：`npm test`
2. 手动功能测试
3. 浏览器兼容性测试

## 部署选项

### 静态文件服务器

- **Nginx** - 高性能Web服务器
- **Apache** - 传统Web服务器
- **IIS** - Windows服务器

### 云平台部署

- **GitHub Pages** - 免费静态托管
- **Netlify** - 现代化部署平台
- **Vercel** - 前端优化平台
- **AWS S3** - 云存储静态托管

### 容器化部署

- **Docker** - 容器化部署
- **Kubernetes** - 容器编排
- **Docker Compose** - 本地容器管理

## 安全考虑

### 数据安全

- **本地存储** - 数据不离开用户浏览器
- **无网络传输** - 避免数据泄露风险
- **用户控制** - 用户完全控制数据

### 应用安全

- **HTTPS部署** - 加密传输
- **CSP策略** - 内容安全策略
- **XSS防护** - 跨站脚本攻击防护

## 性能优化

### 构建优化

- **代码分割** - 按需加载
- **资源压缩** - Gzip压缩
- **缓存策略** - 长期缓存静态资源

### 运行时优化

- **虚拟化列表** - 大量数据展示优化
- **防抖搜索** - 搜索性能优化
- **懒加载** - 按需加载组件

## 扩展性

### 功能扩展

- **规则模板** - 预定义规则模板
- **规则分组** - 按业务分组管理
- **权限控制** - 用户权限管理
- **审计日志** - 操作记录追踪

### 技术扩展

- **后端集成** - 连接真实后端API
- **数据库支持** - 持久化数据存储
- **多租户** - 支持多用户隔离
- **国际化** - 多语言支持

## 维护支持

### 版本管理

- **语义化版本** - 遵循SemVer规范
- **变更日志** - 详细记录版本变更
- **向后兼容** - 保持API稳定性

### 文档维护

- **开发文档** - 技术实现说明
- **用户文档** - 使用指南
- **API文档** - 接口说明
- **部署文档** - 部署指南

### 社区支持

- **问题反馈** - GitHub Issues
- **功能请求** - Feature Requests
- **贡献指南** - Contributing Guidelines
- **代码规范** - Coding Standards

## 许可证

本项目采用MIT许可证，允许自由使用、修改和分发。

## 联系方式

- **项目仓库**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **技术支持**: [Support Email]
- **文档网站**: [Documentation Site]
