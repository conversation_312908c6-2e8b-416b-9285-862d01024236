import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifyJwt from '@fastify/jwt';
import { config } from '../config';

export interface JWTPayload {
  userId: string;
  email?: string;
  roles?: string[];
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: JWTPayload;
  }
}

/**
 * Register JWT authentication plugin
 */
export async function registerAuth(fastify: FastifyInstance): Promise<void> {
  if (!config.auth.enabled) {
    fastify.log.info('Authentication is disabled');
    return;
  }

  // Register JWT plugin
  await fastify.register(fastifyJwt, {
    secret: config.auth.jwtSecret,
    sign: {
      expiresIn: '24h'
    }
  });

  // Add authentication decorator
  fastify.decorate('authenticate', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.code(401).send({ error: 'Unauthorized' });
    }
  });

  // Add optional authentication decorator
  fastify.decorate('optionalAuth', async function (request: FastifyRequest, _reply: FastifyReply) {
    try {
      await request.jwtVerify();
    } catch {
      // Ignore errors for optional auth
    }
  });
}

/**
 * Authentication hook for routes that require authentication
 */
export async function requireAuth(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  if (!config.auth.enabled) {
    return;
  }

  try {
    await request.jwtVerify();
  } catch (err) {
    reply.code(401).send({ error: 'Unauthorized' });
  }
}

/**
 * Optional authentication hook
 */
export async function optionalAuth(request: FastifyRequest, _reply: FastifyReply): Promise<void> {
  if (!config.auth.enabled) {
    return;
  }

  try {
    await request.jwtVerify();
  } catch {
    // Ignore errors for optional auth
  }
}
