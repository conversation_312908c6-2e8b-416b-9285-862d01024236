import { config as dotenvConfig } from 'dotenv';
import { AppConfig } from '../types/config';

// Load environment variables
dotenvConfig();

/**
 * Parse duration string (e.g., "30s", "5m") to milliseconds
 */
function parseDuration(value: string): number {
  const match = value.match(/^(\d+)([smh])$/);
  if (!match) {
    throw new Error(`Invalid duration format: ${value}`);
  }
  
  const num = parseInt(match[1], 10);
  const unit = match[2];
  
  switch (unit) {
    case 's':
      return num * 1000;
    case 'm':
      return num * 60 * 1000;
    case 'h':
      return num * 60 * 60 * 1000;
    default:
      throw new Error(`Unknown duration unit: ${unit}`);
  }
}

/**
 * Get environment variable with default value
 */
function getEnv(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

/**
 * Get environment variable as integer
 */
function getEnvAsInt(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (!value) return defaultValue;
  
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Get environment variable as boolean
 */
function getEnvAsBool(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  if (!value) return defaultValue;
  
  return value.toLowerCase() === 'true';
}

/**
 * Get environment variable as duration in milliseconds
 */
function getEnvAsDuration(key: string, defaultValue: string): number {
  const value = process.env[key];
  try {
    return parseDuration(value || defaultValue);
  } catch {
    return parseDuration(defaultValue);
  }
}

/**
 * Load application configuration from environment variables
 */
export function loadConfig(): AppConfig {
  return {
    server: {
      port: getEnvAsInt('SERVER_PORT', 8080),
      host: getEnv('SERVER_HOST', '0.0.0.0'),
      readTimeout: getEnvAsDuration('SERVER_READ_TIMEOUT', '30s'),
      writeTimeout: getEnvAsDuration('SERVER_WRITE_TIMEOUT', '30s'),
      idleTimeout: getEnvAsDuration('SERVER_IDLE_TIMEOUT', '120s')
    },
    auth: {
      enabled: getEnvAsBool('AUTH_ENABLED', false),
      jwtSecret: getEnv('JWT_SECRET', 'your-secret-key'),
      tokenHeader: getEnv('TOKEN_HEADER', 'Authorization')
    },
    rules: {
      configFile: getEnv('RULES_CONFIG_FILE', 'config/rules.json'),
      reloadEnabled: getEnvAsBool('RULES_RELOAD_ENABLED', true),
      reloadInterval: getEnvAsDuration('RULES_RELOAD_INTERVAL', '5m')
    },
    logging: {
      level: getEnv('LOG_LEVEL', 'info'),
      format: getEnv('LOG_FORMAT', 'json')
    }
  };
}

// Export singleton config instance
export const config = loadConfig();
