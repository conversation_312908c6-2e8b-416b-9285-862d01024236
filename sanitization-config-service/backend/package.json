{"name": "sanitization-config-backend", "version": "1.0.0", "description": "Node.js backend for sanitization configuration service", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "typecheck": "tsc --noEmit"}, "keywords": ["fastify", "typescript", "sanitization", "config"], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/node": "^24.0.14", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/ui": "^3.2.4", "eslint": "^9.31.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/rate-limit": "^10.3.0", "dotenv": "^17.2.0", "fastify": "^5.4.0", "fastify-metrics": "^12.1.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0"}}