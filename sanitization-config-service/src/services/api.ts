import {
  SanitizationConfig,
  SanitizationRule,
  HealthResponse,
  MetricsResponse,
  ApiResponse,
  BatchOperationResponse,
  ValidationResponse
} from '../types';

// Local storage keys
const STORAGE_KEYS = {
  RULES: 'sanitization_rules',
  CONFIG: 'sanitization_config',
  GLOBAL_ENABLED: 'sanitization_global_enabled'
};

// Default configuration
const DEFAULT_CONFIG: SanitizationConfig = {
  enabled: true,
  rules: [
    {
      id: 'phone-rule',
      name: '手机号脱敏',
      description: '对手机号进行脱敏处理，保留前3位和后4位',
      type: 'PATTERN',
      pattern: '1[3-9]\\d{9}',
      fieldNames: ['phone', 'mobile', 'phoneNumber'],
      maskValue: '***-****-****',
      severity: 'HIGH',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'email-rule',
      name: '邮箱脱敏',
      description: '对邮箱地址进行脱敏处理',
      type: 'PATTERN',
      pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
      fieldNames: ['email', 'emailAddress'],
      maskValue: '***@***.***',
      severity: 'MEDIUM',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'idcard-rule',
      name: '身份证脱敏',
      description: '对身份证号进行脱敏处理',
      type: 'PATTERN',
      pattern: '[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]',
      fieldNames: ['idCard', 'identityCard', 'id'],
      maskValue: '****-****-****-****',
      severity: 'CRITICAL',
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]
};

// Utility functions for local storage
const getStoredData = <T>(key: string, defaultValue: T): T => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key ${key}:`, error);
    return defaultValue;
  }
};

const setStoredData = <T>(key: string, data: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error writing to localStorage key ${key}:`, error);
  }
};

// Initialize default data if not exists
const initializeDefaultData = () => {
  if (!localStorage.getItem(STORAGE_KEYS.CONFIG)) {
    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
  }
  if (!localStorage.getItem(STORAGE_KEYS.GLOBAL_ENABLED)) {
    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);
  }
};

// Initialize on module load
initializeDefaultData();

export const sanitizationApi = {
  // Get all sanitization rules
  getRules: async (serviceName?: string): Promise<SanitizationConfig> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    const globalEnabled = getStoredData(STORAGE_KEYS.GLOBAL_ENABLED, true);

    return {
      ...config,
      enabled: globalEnabled
    };
  },

  // Create a new rule
  createRule: async (rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    const newRule: SanitizationRule = {
      ...rule,
      id: rule.id || `rule-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedConfig = {
      ...config,
      rules: [...config.rules, newRule]
    };

    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);

    return {
      success: true,
      data: newRule,
      message: 'Rule created successfully'
    };
  },

  // Update an existing rule
  updateRule: async (ruleId: string, rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);

    if (ruleIndex === -1) {
      throw new Error('Rule not found');
    }

    const updatedRule: SanitizationRule = {
      ...rule,
      id: ruleId,
      createdAt: config.rules[ruleIndex].createdAt,
      updatedAt: new Date().toISOString()
    };

    const updatedConfig = {
      ...config,
      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)
    };

    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);

    return {
      success: true,
      data: updatedRule,
      message: 'Rule updated successfully'
    };
  },

  // Delete a rule
  deleteRule: async (ruleId: string): Promise<ApiResponse<any>> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    const updatedConfig = {
      ...config,
      rules: config.rules.filter(r => r.id !== ruleId)
    };

    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);

    return {
      success: true,
      data: null,
      message: 'Rule deleted successfully'
    };
  },

  // Toggle a specific rule's enabled status
  toggleRule: async (ruleId: string, enabled: boolean): Promise<ApiResponse<SanitizationRule>> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    const ruleIndex = config.rules.findIndex(r => r.id === ruleId);

    if (ruleIndex === -1) {
      throw new Error('Rule not found');
    }

    const updatedRule = {
      ...config.rules[ruleIndex],
      enabled,
      updatedAt: new Date().toISOString()
    };

    const updatedConfig = {
      ...config,
      rules: config.rules.map((r, index) => index === ruleIndex ? updatedRule : r)
    };

    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);

    return {
      success: true,
      data: updatedRule,
      message: `Rule ${enabled ? 'enabled' : 'disabled'} successfully`
    };
  },

  // Batch operations on multiple rules
  batchOperation: async (ruleIds: string[], operation: 'enable' | 'disable' | 'delete'): Promise<BatchOperationResponse> => {
    await new Promise(resolve => setTimeout(resolve, 200));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
    let updatedRules = [...config.rules];
    let successCount = 0;
    let failureCount = 0;

    for (const ruleId of ruleIds) {
      const ruleIndex = updatedRules.findIndex(r => r.id === ruleId);
      if (ruleIndex !== -1) {
        if (operation === 'delete') {
          updatedRules = updatedRules.filter(r => r.id !== ruleId);
        } else {
          updatedRules[ruleIndex] = {
            ...updatedRules[ruleIndex],
            enabled: operation === 'enable',
            updatedAt: new Date().toISOString()
          };
        }
        successCount++;
      } else {
        failureCount++;
      }
    }

    const updatedConfig = {
      ...config,
      rules: updatedRules
    };

    setStoredData(STORAGE_KEYS.CONFIG, updatedConfig);

    return {
      success: failureCount === 0,
      successCount,
      failureCount,
      message: `Batch operation completed: ${successCount} successful, ${failureCount} failed`
    };
  },

  // Validate a rule configuration
  validateRule: async (rule: SanitizationRule, testInput?: string): Promise<ValidationResponse> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    const errors: string[] = [];

    // Basic validation
    if (!rule.name?.trim()) {
      errors.push('Rule name is required');
    }

    if (!rule.type) {
      errors.push('Rule type is required');
    }

    if (rule.type === 'PATTERN' && !rule.pattern?.trim()) {
      errors.push('Pattern is required for PATTERN type rules');
    }

    if (rule.type === 'FIELD_NAME' && (!rule.fieldNames || rule.fieldNames.length === 0)) {
      errors.push('Field names are required for FIELD_NAME type rules');
    }

    if (!rule.maskValue?.trim()) {
      errors.push('Mask value is required');
    }

    // Pattern validation
    if (rule.type === 'PATTERN' && rule.pattern) {
      try {
        new RegExp(rule.pattern);
      } catch (e) {
        errors.push('Invalid regular expression pattern');
      }
    }

    // Test input validation if provided
    let testResult = null;
    if (testInput && rule.pattern && errors.length === 0) {
      try {
        const regex = new RegExp(rule.pattern, 'g');
        const matches = testInput.match(regex);
        testResult = {
          input: testInput,
          matches: matches || [],
          masked: matches ? testInput.replace(regex, rule.maskValue) : testInput
        };
      } catch (e) {
        errors.push('Error testing pattern against input');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      testResult,
      message: errors.length === 0 ? 'Validation successful' : 'Validation failed',
      timestamp: Date.now(),
      testOutput: testResult ? testResult.masked : undefined
    };
  },

  // Reload rules from configuration file
  reloadRules: async (): Promise<ApiResponse<any>> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    // In a real frontend-only app, this would reset to default configuration
    setStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);

    return {
      success: true,
      data: null,
      message: 'Rules reloaded to default configuration'
    };
  },

  // Toggle global sanitization switch
  toggleGlobalSwitch: async (enabled: boolean): Promise<{ enabled: boolean; message: string; timestamp: number }> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    setStoredData(STORAGE_KEYS.GLOBAL_ENABLED, enabled);

    return {
      enabled,
      message: `Global sanitization ${enabled ? 'enabled' : 'disabled'}`,
      timestamp: Date.now()
    };
  },

  // Get service health
  getHealth: async (): Promise<HealthResponse> => {
    await new Promise(resolve => setTimeout(resolve, 50));

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: Date.now(),
      checks: {
        storage: 'healthy',
        memory: 'healthy'
      }
    };
  },

  // Get service metrics
  getMetrics: async (): Promise<MetricsResponse> => {
    await new Promise(resolve => setTimeout(resolve, 50));

    const config = getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);

    return {
      totalRules: config.rules.length,
      enabledRules: config.rules.filter(r => r.enabled).length,
      disabledRules: config.rules.filter(r => !r.enabled).length,
      rulesByType: {
        FIELD_NAME: config.rules.filter(r => r.type === 'FIELD_NAME').length,
        PATTERN: config.rules.filter(r => r.type === 'PATTERN').length,
        CONTENT_TYPE: config.rules.filter(r => r.type === 'CONTENT_TYPE').length,
        CUSTOM: config.rules.filter(r => r.type === 'CUSTOM').length
      },
      rulesBySeverity: {
        CRITICAL: config.rules.filter(r => r.severity === 'CRITICAL').length,
        HIGH: config.rules.filter(r => r.severity === 'HIGH').length,
        MEDIUM: config.rules.filter(r => r.severity === 'MEDIUM').length,
        LOW: config.rules.filter(r => r.severity === 'LOW').length
      }
    };
  },

  // Export rules configuration
  exportRules: async (): Promise<SanitizationConfig> => {
    await new Promise(resolve => setTimeout(resolve, 100));

    return getStoredData(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG);
  },

  // Import rules configuration
  importRules: async (config: SanitizationConfig): Promise<ApiResponse<any>> => {
    await new Promise(resolve => setTimeout(resolve, 200));

    // Validate imported configuration
    if (!config || !Array.isArray(config.rules)) {
      throw new Error('Invalid configuration format');
    }

    // Add timestamps to imported rules
    const processedConfig = {
      ...config,
      rules: config.rules.map(rule => ({
        ...rule,
        createdAt: rule.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))
    };

    setStoredData(STORAGE_KEYS.CONFIG, processedConfig);

    return {
      success: true,
      data: processedConfig,
      message: `Successfully imported ${config.rules.length} rules`
    };
  },
};
