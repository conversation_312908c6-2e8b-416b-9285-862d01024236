{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 18H2a4 4 0 0 0 4 4h12a4 4 0 0 0 4-4Z\",\n  key: \"1404fh\"\n}], [\"path\", {\n  d: \"M21 14 10 2 3 14h18Z\",\n  key: \"1nzg7v\"\n}], [\"path\", {\n  d: \"M10 2v16\",\n  key: \"1labyt\"\n}]];\nconst Sailboat = createLucideIcon(\"sailboat\", __iconNode);\nexport { __iconNode, Sailboat as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Sailboat", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/sailboat.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M22 18H2a4 4 0 0 0 4 4h12a4 4 0 0 0 4-4Z', key: '1404fh' }],\n  ['path', { d: 'M21 14 10 2 3 14h18Z', key: '1nzg7v' }],\n  ['path', { d: 'M10 2v16', key: '1labyt' }],\n];\n\n/**\n * @component @name Sailboat\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMThIMmE0IDQgMCAwIDAgNCA0aDEyYTQgNCAwIDAgMCA0LTRaIiAvPgogIDxwYXRoIGQ9Ik0yMSAxNCAxMCAyIDMgMTRoMThaIiAvPgogIDxwYXRoIGQ9Ik0xMCAydjE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sailboat\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sailboat = createLucideIcon('sailboat', __iconNode);\n\nexport default Sailboat;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}