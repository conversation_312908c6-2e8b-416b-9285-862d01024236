export interface ServerConfig {
  port: number;
  host: string;
  readTimeout: number;
  writeTimeout: number;
  idleTimeout: number;
}

export interface AuthConfig {
  enabled: boolean;
  jwtSecret: string;
  tokenHeader: string;
}

export interface RulesFileConfig {
  configFile: string;
  reloadEnabled: boolean;
  reloadInterval: number;
}

export interface LoggingConfig {
  level: string;
  format: string;
}

export interface AppConfig {
  server: ServerConfig;
  auth: AuthConfig;
  rules: RulesFileConfig;
  logging: LoggingConfig;
}
