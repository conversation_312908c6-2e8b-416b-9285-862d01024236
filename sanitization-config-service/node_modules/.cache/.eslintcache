[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts": "4"}, {"size": 554, "mtime": 1752811811415, "results": "5", "hashOfConfig": "6"}, {"size": 419, "mtime": 1752814315037, "results": "7", "hashOfConfig": "6"}, {"size": 9746, "mtime": 1752814341384, "results": "8", "hashOfConfig": "6"}, {"size": 12417, "mtime": 1752812726008, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jk5nvp", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts", [], []]