# 脱敏配置说明文档

## 📋 概述

本文档详细说明了数据脱敏系统的配置方法、规则定义、参数说明和最佳实践。

## 🔧 系统配置

### 环境变量配置

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `SERVER_PORT` | `8081` | 服务器端口 | `8081` |
| `SERVER_HOST` | `0.0.0.0` | 服务器地址 | `0.0.0.0` |
| `RULES_CONFIG_FILE` | `config/rules.json` | 规则配置文件路径 | `config/rules.json` |
| `AUTH_ENABLED` | `false` | 是否启用认证 | `true/false` |
| `JWT_SECRET` | - | JWT密钥 | `your-secret-key` |
| `LOG_LEVEL` | `info` | 日志级别 | `debug/info/warn/error` |
| `LOG_FORMAT` | `json` | 日志格式 | `json/text` |

### 启动配置示例

```bash
# 开发环境
SERVER_PORT=8081 \
LOG_LEVEL=debug \
go run main.go

# 生产环境
SERVER_PORT=8081 \
AUTH_ENABLED=true \
JWT_SECRET=your-production-secret \
LOG_LEVEL=info \
./sanitization-config-service
```

## 📝 规则配置结构

### 配置文件格式

规则配置采用JSON格式，支持完整的规则定义和全局设置：

```json
{
  "version": "1.0.0",
  "enabled": true,
  "markersEnabled": false,
  "markerFormat": "BRACKET",
  "timestamp": 1703123456789,
  "rules": [
    {
      "id": "rule-unique-id",
      "name": "规则显示名称",
      "description": "规则详细描述",
      "type": "FIELD_NAME",
      "severity": "HIGH",
      "enabled": true,
      "priority": 100,
      "fieldNames": ["password", "secret"],
      "pattern": "",
      "contentTypes": [],
      "maskValue": "****",
      "markerType": "",
      "preserveFormat": false,
      "preserveLength": 0,
      "includeServices": [],
      "excludeServices": [],
      "conditions": {}
    }
  ]
}
```

### 全局配置参数

#### 基础设置
- **version**: 配置版本号，用于版本管理
- **enabled**: 全局脱敏开关，控制整个系统是否启用脱敏
- **timestamp**: 配置最后修改时间戳

#### 标记设置
- **markersEnabled**: 是否启用脱敏标记
- **markerFormat**: 标记格式
  - `BRACKET`: 方括号格式 `[MASKED]`
  - `ANGLE`: 尖括号格式 `<MASKED>`
  - `CUSTOM`: 自定义格式

## 🎯 规则配置详解

### 规则基本属性

#### 必填字段
- **id**: 规则唯一标识符，不可重复
- **name**: 规则显示名称
- **type**: 规则类型（见下方详细说明）
- **severity**: 严重程度
- **enabled**: 规则是否启用
- **priority**: 优先级（数值越小优先级越高）
- **maskValue**: 脱敏替换值

#### 可选字段
- **description**: 规则描述
- **markerType**: 标记类型
- **preserveFormat**: 是否保持原格式
- **preserveLength**: 保留长度
- **includeServices**: 包含的服务列表
- **excludeServices**: 排除的服务列表
- **conditions**: 额外条件

### 规则类型详解

#### 1. FIELD_NAME - 字段名匹配

根据字段名称进行脱敏，适用于已知敏感字段。

```json
{
  "id": "password-fields",
  "name": "密码字段",
  "type": "FIELD_NAME",
  "severity": "CRITICAL",
  "enabled": true,
  "priority": 10,
  "fieldNames": [
    "password",
    "passwd", 
    "pwd",
    "secret",
    "token",
    "key"
  ],
  "maskValue": "********",
  "description": "脱敏所有密码相关字段"
}
```

**适用场景**:
- 用户密码字段
- API密钥字段
- 认证令牌字段
- 其他已知敏感字段

#### 2. PATTERN - 正则表达式匹配

使用正则表达式匹配内容进行脱敏，适用于格式化数据。

```json
{
  "id": "email-pattern",
  "name": "邮箱地址",
  "type": "PATTERN",
  "severity": "MEDIUM",
  "enabled": true,
  "priority": 50,
  "pattern": "([a-zA-Z0-9._%+-]{1,3})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})",
  "maskValue": "$1***@$2",
  "preserveFormat": true,
  "description": "脱敏邮箱地址，保留前3位和域名"
}
```

**常用正则模式**:
```json
{
  "手机号": "1[3-9]\\d{9}",
  "身份证": "\\d{17}[\\dXx]",
  "银行卡": "\\d{16,19}",
  "IP地址": "\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b",
  "URL": "https?://[\\w\\-]+(\\.[\\w\\-]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?"
}
```

#### 3. CONTENT_TYPE - 内容类型匹配

根据HTTP内容类型进行脱敏，适用于特定格式的数据。

```json
{
  "id": "json-sensitive",
  "name": "JSON敏感数据",
  "type": "CONTENT_TYPE",
  "severity": "HIGH",
  "enabled": true,
  "priority": 30,
  "contentTypes": [
    "application/json",
    "application/x-www-form-urlencoded"
  ],
  "fieldNames": ["creditCard", "ssn"],
  "maskValue": "***MASKED***",
  "description": "脱敏JSON格式中的敏感字段"
}
```

#### 4. CUSTOM - 自定义规则

自定义脱敏逻辑，适用于复杂场景。

```json
{
  "id": "custom-business",
  "name": "业务自定义",
  "type": "CUSTOM",
  "severity": "HIGH",
  "enabled": true,
  "priority": 20,
  "conditions": {
    "businessType": "payment",
    "dataLevel": "sensitive"
  },
  "maskValue": "[BUSINESS_MASKED]",
  "description": "业务特定的自定义脱敏规则"
}
```

### 严重程度分级

| 级别 | 说明 | 颜色标识 | 适用场景 |
|------|------|----------|----------|
| `CRITICAL` | 关键 | 🔴 红色 | 密码、密钥、证书 |
| `HIGH` | 高 | 🟠 橙色 | 身份证、银行卡、手机号 |
| `MEDIUM` | 中等 | 🟡 黄色 | 邮箱、姓名、地址 |
| `LOW` | 低 | 🟢 绿色 | 用户ID、昵称 |

### 优先级设置

优先级决定规则的执行顺序，数值越小优先级越高：

- **1-50**: 高优先级（关键敏感数据）
- **51-100**: 中优先级（一般敏感数据）
- **101-500**: 低优先级（辅助脱敏）
- **501+**: 最低优先级（可选脱敏）

## 🎨 脱敏策略配置

### 完全脱敏
```json
{
  "maskValue": "********",
  "preserveFormat": false,
  "preserveLength": 0
}
```

### 部分脱敏
```json
{
  "pattern": "([a-zA-Z0-9._%+-]{1,3})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})",
  "maskValue": "$1***@$2",
  "preserveFormat": true
}
```

### 长度保持
```json
{
  "maskValue": "*",
  "preserveFormat": true,
  "preserveLength": -1
}
```

### 格式保持
```json
{
  "pattern": "(\\d{3})\\d{4}(\\d{4})",
  "maskValue": "$1****$2",
  "preserveFormat": true
}
```

## 🔍 服务范围配置

### 包含特定服务
```json
{
  "includeServices": [
    "user-service",
    "payment-service",
    "auth-service"
  ]
}
```

### 排除特定服务
```json
{
  "excludeServices": [
    "log-service",
    "monitor-service"
  ]
}
```

### 条件匹配
```json
{
  "conditions": {
    "environment": "production",
    "dataLevel": "sensitive",
    "userRole": "external"
  }
}
```

## 📊 配置示例

### 完整配置示例
```json
{
  "version": "1.2.0",
  "enabled": true,
  "markersEnabled": true,
  "markerFormat": "BRACKET",
  "timestamp": 1703123456789,
  "rules": [
    {
      "id": "critical-passwords",
      "name": "关键密码字段",
      "description": "完全脱敏所有密码相关字段",
      "type": "FIELD_NAME",
      "severity": "CRITICAL",
      "enabled": true,
      "priority": 10,
      "fieldNames": ["password", "passwd", "pwd", "secret", "privateKey"],
      "maskValue": "********",
      "markerType": "PASSWORD",
      "preserveFormat": false,
      "includeServices": ["user-service", "auth-service"]
    },
    {
      "id": "phone-numbers",
      "name": "手机号码",
      "description": "脱敏手机号码，保留前3位和后4位",
      "type": "PATTERN",
      "severity": "HIGH",
      "enabled": true,
      "priority": 20,
      "pattern": "(1[3-9]\\d)(\\d{4})(\\d{4})",
      "maskValue": "$1****$3",
      "preserveFormat": true,
      "includeServices": ["user-service", "crm-service"]
    },
    {
      "id": "email-addresses",
      "name": "邮箱地址",
      "description": "脱敏邮箱地址，保留前3位和域名",
      "type": "PATTERN",
      "severity": "MEDIUM",
      "enabled": true,
      "priority": 30,
      "pattern": "([a-zA-Z0-9._%+-]{1,3})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})",
      "maskValue": "$1***@$2",
      "preserveFormat": true
    }
  ]
}
```

## 🚀 最佳实践

### 1. 规则设计原则
- **最小化原则**: 只脱敏必要的敏感数据
- **分层设计**: 按严重程度分层配置
- **服务隔离**: 不同服务使用不同规则
- **性能优先**: 优化正则表达式性能

### 2. 优先级规划
```
1-10:   密码、密钥、证书等关键信息
11-30:  身份证、银行卡等高敏感信息  
31-50:  手机号、邮箱等中敏感信息
51-100: 姓名、地址等低敏感信息
100+:   其他可选脱敏信息
```

### 3. 测试验证
- 使用规则验证功能测试正则表达式
- 在测试环境验证脱敏效果
- 监控脱敏性能影响
- 定期审查规则有效性

### 4. 维护管理
- 定期备份配置文件
- 版本化管理配置变更
- 监控规则使用统计
- 及时更新过时规则

## 🔧 故障排除

### 常见问题

1. **规则不生效**
   - 检查规则是否启用
   - 验证优先级设置
   - 确认服务匹配条件

2. **正则表达式错误**
   - 使用规则验证功能
   - 检查转义字符
   - 测试匹配效果

3. **性能问题**
   - 优化复杂正则表达式
   - 调整规则优先级
   - 限制规则作用范围

4. **配置文件错误**
   - 验证JSON格式
   - 检查必填字段
   - 确认字段类型

### 调试方法

1. **启用调试日志**
```bash
LOG_LEVEL=debug go run main.go
```

2. **使用API测试**
```bash
# 验证规则
curl -X POST http://localhost:8081/api/sanitization/rules/validate \
  -H "Content-Type: application/json" \
  -d '{"rule": {...}, "testInput": "test data"}'
```

3. **查看系统指标**
```bash
curl http://localhost:8081/metrics
```

## 📚 参考资料

- [API接口文档](./README_EN.md#api-endpoints)
- [功能特性说明](./ENHANCED_FEATURES.md)
- [前端使用指南](./FRONTEND_README.md)
- [项目总结](./PROJECT_SUMMARY.md)
