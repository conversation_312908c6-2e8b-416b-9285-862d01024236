# 脱敏配置服务前端

这是一个现代化的 React TypeScript 前端应用，用于管理数据脱敏规则和配置。

## 🚀 功能特性

### 1. 仪表板 (Dashboard)
- **服务健康状态监控**: 实时显示服务运行状态
- **规则统计**: 显示总规则数、启用规则数、禁用规则数
- **按类型分类**: 展示不同类型规则的分布情况
- **按严重程度分类**: 展示不同严重程度规则的分布
- **系统信息**: 显示版本信息和最后更新时间
- **自动刷新**: 每30秒自动刷新数据

### 2. 规则管理 (Rules Management)
- **规则列表展示**: 以表格形式展示所有脱敏规则
- **多维度筛选**:
  - 按规则名称、描述、ID搜索
  - 按规则类型筛选 (FIELD_NAME, PATTERN, CONTENT_TYPE, CUSTOM)
  - 按严重程度筛选 (CRITICAL, HIGH, MEDIUM, LOW)
  - 按服务名称筛选
  - 仅显示启用的规则
- **规则详情**: 显示规则的详细配置信息
- **状态指示**: 清晰的启用/禁用状态显示
- **导出功能**: 支持导出配置为JSON格式
- **配置重载**: 支持从配置文件重新加载规则

### 3. 配置管理 (Configuration Manager)
- **全局设置管理**:
  - 启用/禁用整个脱敏系统
  - 启用/禁用标记功能
  - 配置标记格式 (BRACKET, ASTERISK, HASH, NONE)
  - 版本管理
- **高级设置**: 管理全局配置参数
- **实时预览**: JSON格式的配置预览
- **配置导出**: 下载完整配置文件

## 🛠 技术栈

- **React 18** + **TypeScript**: 现代化的前端框架
- **Tailwind CSS**: 实用优先的CSS框架，提供现代化UI设计
- **React Router**: 单页应用路由管理
- **Axios**: HTTP客户端，用于API调用
- **Lucide React**: 现代化的图标库
- **React Hot Toast**: 优雅的通知提示组件
- **CRACO**: Create React App配置覆盖

## 🎨 设计特色

### 响应式设计
- 支持桌面端和移动端
- 自适应布局，在不同屏幕尺寸下都有良好体验

### 现代化UI
- 使用Tailwind CSS构建的现代化界面
- 一致的颜色主题和设计语言
- 直观的用户交互体验

### 实时更新
- 自动刷新数据
- 实时状态监控
- 即时反馈和通知

## 🚀 快速开始

### 前置要求
- Node.js 16+ 和 npm
- 后端服务运行在端口 8081

### 安装和运行

1. **安装依赖**:
```bash
cd frontend
npm install
```

2. **启动开发服务器**:
```bash
npm start
```

3. **访问应用**: 打开 [http://localhost:3000](http://localhost:3000)

### 一键启动脚本
项目根目录提供了便捷的启动脚本：
```bash
./start-dev.sh
```
这将同时启动后端服务(端口8081)和前端应用(端口3000)。

## 📁 项目结构

```
frontend/
├── src/
│   ├── components/          # React组件
│   │   ├── Dashboard.tsx    # 仪表板组件
│   │   ├── RulesList.tsx    # 规则列表组件
│   │   ├── ConfigManager.tsx # 配置管理组件
│   │   └── Navigation.tsx   # 导航组件
│   ├── services/           # API服务
│   │   └── api.ts          # API接口定义
│   ├── types/              # TypeScript类型定义
│   │   └── index.ts        # 数据类型
│   ├── utils/              # 工具函数
│   │   └── index.ts        # 通用工具
│   └── App.tsx             # 主应用组件
├── public/                 # 静态资源
├── .env                    # 环境配置
├── tailwind.config.js      # Tailwind配置
├── craco.config.js         # CRACO配置
└── package.json            # 项目依赖
```

## 🔧 配置说明

### 环境变量
在 `.env` 文件中配置：
```env
REACT_APP_API_URL=http://localhost:8081
GENERATE_SOURCEMAP=false
```

### API集成
前端与以下后端接口集成：
- `GET /health` - 服务健康检查
- `GET /metrics` - 服务指标统计
- `GET /api/sanitization/rules` - 获取脱敏规则
- `POST /api/sanitization/rules/reload` - 重载配置

## 🎯 使用指南

### 1. 查看系统状态
- 访问仪表板页面查看服务整体状态
- 监控规则统计和系统健康状况

### 2. 管理脱敏规则
- 在规则页面查看所有配置的脱敏规则
- 使用搜索和筛选功能快速找到特定规则
- 查看规则详细配置和状态

### 3. 更新配置
- 在配置页面修改全局设置
- 重载配置文件以应用更改
- 导出配置进行备份

### 4. 服务特定规则
- 在规则页面的服务名称字段输入特定服务名
- 查看仅适用于该服务的规则

## 🔄 配置更新流程

1. **修改配置文件**: 直接编辑后端的 `config/rules.json` 文件
2. **重载配置**: 在前端界面点击"Reload Config"按钮
3. **验证更改**: 查看规则列表确认更改已生效
4. **导出备份**: 使用导出功能保存当前配置

## 🚀 生产部署

### 构建生产版本
```bash
npm run build
```

### Docker部署
```dockerfile
FROM nginx:alpine
COPY build/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📝 开发说明

- 遵循TypeScript最佳实践
- 使用Tailwind CSS进行样式设计
- 包含适当的错误处理和加载状态
- 编写有意义的提交信息

这个前端应用提供了完整的脱敏配置管理界面，支持实时监控、规则管理和配置更新等核心功能。
