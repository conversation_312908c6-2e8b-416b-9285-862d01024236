# Sanitization Config Service

A comprehensive microservice for managing data sanitization rules with a modern web interface.

## 🚀 Features

### Backend API
- Complete CRUD operations for sanitization rules
- Batch operations (enable/disable/delete multiple rules)
- Rule validation and testing
- Import/Export configuration
- Dynamic rule loading and reloading
- Service-specific rule filtering
- Health checks and metrics
- Configurable authentication

### Frontend Interface
- Modern React-based web interface
- Interactive dashboard with statistics
- Advanced rule editor with validation
- Batch operations and bulk management
- Import/Export functionality
- Responsive design for all devices
- Real-time updates and notifications

## 🏃 Quick Start

### Development Environment

Use the development script to start both backend and frontend:

```bash
# Start both services
./start-dev.sh
```

This will start:
- Backend API on http://localhost:8081
- Frontend UI on http://localhost:3000

### Manual Setup

#### Backend Service
```bash
# Install Go dependencies
go mod download

# Start backend service
SERVER_PORT=8081 go run main.go
```

#### Frontend Service
```bash
# Install Node.js dependencies
cd frontend
npm install

# Start frontend development server
npm start
```

## 📡 API Endpoints

### Health & Metrics
- `GET /health` - Service health status
- `GET /metrics` - Service metrics and statistics

### Rules Management
- `GET /api/sanitization/rules` - Get all sanitization rules
- `POST /api/sanitization/rules` - Create new rule
- `PUT /api/sanitization/rules/{id}` - Update existing rule
- `DELETE /api/sanitization/rules/{id}` - Delete rule
- `POST /api/sanitization/rules/{id}/toggle` - Toggle rule status

### Batch Operations
- `POST /api/sanitization/rules/batch` - Batch operations on multiple rules

### Validation & Testing
- `POST /api/sanitization/rules/validate` - Validate rule configuration

### Import/Export
- `GET /api/sanitization/rules/export` - Export configuration
- `POST /api/sanitization/rules/import` - Import configuration

### Configuration Management
- `POST /api/sanitization/rules/reload` - Reload rules from file
- `POST /api/sanitization/toggle` - Toggle global sanitization switch

## ⚙️ Configuration

Configure the service using environment variables:

- `SERVER_PORT` - Server port (default: 8081)
- `SERVER_HOST` - Server host (default: 0.0.0.0)
- `RULES_CONFIG_FILE` - Path to rules configuration file (default: config/rules.json)
- `AUTH_ENABLED` - Enable authentication (default: false)
- `JWT_SECRET` - JWT secret for authentication
- `LOG_LEVEL` - Logging level (default: info)

## 🛠️ Development

### Prerequisites
- Go 1.21 or later
- Node.js 16 or later
- Docker (optional)

### Project Structure
```
sanitization-config-service/
├── main.go                 # Main application entry
├── handlers/              # HTTP handlers
├── service/               # Business logic
├── models/                # Data models
├── config/                # Configuration files
├── middleware/            # HTTP middleware
├── frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── services/      # API services
│   │   └── types/         # TypeScript types
│   └── public/
├── start.sh              # Production startup script
├── start-dev.sh          # Development startup script
├── test_service.sh       # Backend API testing
└── test-frontend.sh      # Frontend testing
```

### Available Scripts

#### Backend Testing
```bash
# Test backend API endpoints
./test_service.sh
```

#### Frontend Testing
```bash
# Test frontend functionality
./test-frontend.sh
```

#### Production Deployment
```bash
# Start production service
./start.sh
```

## 🎯 Usage Examples

### Web Interface
1. Open http://localhost:3000 in your browser
2. View dashboard for system overview
3. Navigate to Rules tab for rule management
4. Create, edit, or delete rules as needed
5. Use batch operations for efficiency
6. Export/import configurations for backup

### API Usage
```bash
# Get all rules
curl http://localhost:8081/api/sanitization/rules

# Create a new rule
curl -X POST http://localhost:8081/api/sanitization/rules \
  -H "Content-Type: application/json" \
  -d '{"id":"test-rule","name":"Test Rule","type":"FIELD_NAME","severity":"LOW","enabled":true,"priority":500,"fieldNames":["test"],"maskValue":"***"}'

# Toggle global sanitization
curl -X POST http://localhost:8081/api/sanitization/toggle \
  -H "Content-Type: application/json" \
  -d '{"enabled":false}'
```

## 📄 License

MIT License
