import Fastify from 'fastify';
import { config } from './config';
import { createLoggerOptions } from './utils/logger';
import { registerAuth } from './middleware/auth';

// Create a Fastify instance
const fastify = Fastify({
  logger: createLoggerOptions()
});

// Register middleware and plugins
const registerPlugins = async () => {
  // Register authentication middleware
  await registerAuth(fastify);

  // TODO: Register other middleware and routes here
};

// Start the server
const startServer = async () => {
  try {
    await registerPlugins();
    await fastify.listen({
      port: config.server.port,
      host: config.server.host
    });
    fastify.log.info(`Server is running at http://${config.server.host}:${config.server.port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

startServer();

