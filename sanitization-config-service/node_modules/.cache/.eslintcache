[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts": "3"}, {"size": 272, "mtime": 1752814863584, "results": "4", "hashOfConfig": "5"}, {"size": 9746, "mtime": 1752814341384, "results": "6", "hashOfConfig": "5"}, {"size": 12417, "mtime": 1752812726008, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jk5nvp", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts", [], []]