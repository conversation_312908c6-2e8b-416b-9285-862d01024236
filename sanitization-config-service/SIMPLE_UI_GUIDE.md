# 简洁版脱敏规则管理界面

## 🎯 设计理念

新版界面采用简洁设计理念，专注于脱敏规则管理的核心功能，去除了复杂的仪表板和统计图表，提供更直观、高效的用户体验。

## 🖥️ 界面布局

### 顶部导航栏
- **左侧**: 系统标题和图标
- **右侧**: 全局脱敏状态显示和控制按钮

### 主要功能区域
- **规则管理**: 唯一的主要功能页面
- **搜索筛选**: 快速查找和过滤规则
- **批量操作**: 高效的批量管理工具

## 🔧 核心功能

### 1. 全局脱敏开关
位置：顶部导航栏右侧

**功能**:
- 实时显示全局脱敏状态（已启用/已禁用）
- 一键切换全局脱敏开关
- 状态指示灯（绿色=启用，红色=禁用）

**操作**:
- 点击"启用脱敏"或"禁用脱敏"按钮
- 系统会实时更新状态并显示确认消息

### 2. 规则管理
位置：主内容区域

**功能**:
- 查看所有脱敏规则列表
- 创建新的脱敏规则
- 编辑现有规则
- 删除不需要的规则
- 启用/禁用单个规则

**规则信息显示**:
- 规则名称和描述
- 规则类型（字段匹配/正则匹配/内容类型/自定义）
- 严重程度（关键/高/中等/低）
- 启用状态
- 匹配条件（字段名或正则表达式）
- 脱敏值

### 3. 搜索和筛选
位置：规则列表上方

**搜索功能**:
- 支持按规则名称、描述、ID搜索
- 实时搜索，输入即时显示结果

**筛选功能**:
- 按规则类型筛选
- 按严重程度筛选
- 按启用状态筛选

### 4. 批量操作
位置：选择规则后显示

**支持操作**:
- 批量启用规则
- 批量禁用规则
- 批量删除规则

**使用方法**:
1. 勾选需要操作的规则
2. 选择批量操作类型
3. 确认操作

### 5. 导入导出
位置：规则管理页面顶部

**导出功能**:
- 一键导出当前所有规则配置
- 生成JSON格式配置文件
- 文件名包含日期时间戳

**导入功能**:
- 支持导入JSON格式配置文件
- 自动验证配置文件格式
- 导入后自动刷新规则列表

### 6. 配置重载
位置：顶部导航栏

**功能**:
- 从配置文件重新加载规则
- 用于同步外部配置文件的修改
- 操作后显示重载结果

## 🎨 界面特色

### 简洁设计
- 采用浅色主题，界面清爽
- 去除不必要的装饰元素
- 专注于功能性设计

### 中文界面
- 全中文界面，符合国内用户习惯
- 术语统一，易于理解
- 操作提示清晰明确

### 响应式布局
- 支持桌面和移动端访问
- 自适应不同屏幕尺寸
- 保持良好的用户体验

### 状态反馈
- 实时状态指示
- 操作成功/失败提示
- 加载状态显示

## 🚀 使用流程

### 首次使用
1. 启动服务：`./start-dev.sh`
2. 访问界面：http://localhost:3000
3. 查看当前规则列表
4. 根据需要启用全局脱敏

### 日常管理
1. **查看规则**: 直接在主页面查看所有规则
2. **搜索规则**: 使用搜索框快速定位
3. **编辑规则**: 点击规则操作菜单选择编辑
4. **批量操作**: 选择多个规则进行批量处理
5. **导出备份**: 定期导出配置文件备份

### 规则创建
1. 点击"新建规则"按钮
2. 填写规则基本信息
3. 选择规则类型和配置参数
4. 使用测试功能验证规则
5. 保存规则

## 💡 使用技巧

### 规则管理
- 使用描述性的规则名称，便于识别
- 合理设置规则优先级
- 定期检查和清理不使用的规则

### 搜索筛选
- 使用关键词快速定位规则
- 结合多个筛选条件精确查找
- 利用规则ID进行精确搜索

### 批量操作
- 先筛选出目标规则再进行批量操作
- 批量删除前确保已备份重要规则
- 分批处理大量规则，避免一次性操作过多

### 配置管理
- 定期导出配置文件作为备份
- 在生产环境部署前先在测试环境验证
- 使用版本控制管理配置文件变更

## 🔍 故障排除

### 界面无法访问
- 检查前端服务是否正常启动
- 确认端口3000没有被占用
- 查看浏览器控制台错误信息

### 全局开关无效
- 检查后端服务连接状态
- 确认API接口正常响应
- 查看后端服务日志

### 规则操作失败
- 检查规则配置是否正确
- 确认必填字段已填写
- 验证正则表达式语法

### 导入导出问题
- 确认文件格式为JSON
- 检查文件内容结构是否正确
- 查看浏览器下载设置

## 📞 获取帮助

- **配置问题**: 查看 [配置指南](./CONFIGURATION_GUIDE.md)
- **快速上手**: 参考 [快速配置](./QUICK_CONFIG.md)
- **API文档**: 查看 [接口文档](./README_EN.md)
- **完整文档**: 浏览 [文档索引](./DOCS_INDEX.md)

---

💡 **提示**: 新版界面专注于实用性和易用性，如需更多高级功能，可以通过API接口进行扩展。
