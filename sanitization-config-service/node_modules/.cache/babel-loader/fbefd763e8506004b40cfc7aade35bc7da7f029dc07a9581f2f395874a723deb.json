{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 16v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4\",\n  key: \"1r120k\"\n}], [\"path\", {\n  d: \"M7 2h11v4c0 2-2 2-2 4v1\",\n  key: \"dz1920\"\n}], [\"line\", {\n  x1: \"11\",\n  x2: \"18\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"bi1vpe\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst FlashlightOff = createLucideIcon(\"flashlight-off\", __iconNode);\nexport { __iconNode, FlashlightOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x1", "x2", "y1", "y2", "FlashlightOff", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/flashlight-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 16v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4', key: '1r120k' }],\n  ['path', { d: 'M7 2h11v4c0 2-2 2-2 4v1', key: 'dz1920' }],\n  ['line', { x1: '11', x2: '18', y1: '6', y2: '6', key: 'bi1vpe' }],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n];\n\n/**\n * @component @name FlashlightOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTZ2NGEyIDIgMCAwIDEtMiAyaC00YTIgMiAwIDAgMS0yLTJWMTBjMC0yLTItMi0yLTQiIC8+CiAgPHBhdGggZD0iTTcgMmgxMXY0YzAgMi0yIDItMiA0djEiIC8+CiAgPGxpbmUgeDE9IjExIiB4Mj0iMTgiIHkxPSI2IiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iMiIgeDI9IjIyIiB5MT0iMiIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/flashlight-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlashlightOff = createLucideIcon('flashlight-off', __iconNode);\n\nexport default FlashlightOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wDAA0D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GAClE;AAaM,MAAAK,aAAA,GAAgBC,gBAAiB,mBAAkBR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}