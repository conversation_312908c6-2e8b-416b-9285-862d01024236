{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.065 12.493-6.18 1.318a.934.934 0 0 1-1.108-.702l-.537-2.15a1.07 1.07 0 0 1 .691-1.265l13.504-4.44\",\n  key: \"k4qptu\"\n}], [\"path\", {\n  d: \"m13.56 11.747 4.332-.924\",\n  key: \"19l80z\"\n}], [\"path\", {\n  d: \"m16 21-3.105-6.21\",\n  key: \"7oh9d\"\n}], [\"path\", {\n  d: \"M16.485 5.94a2 2 0 0 1 1.455-2.425l1.09-.272a1 1 0 0 1 1.212.727l1.515 6.06a1 1 0 0 1-.727 1.213l-1.09.272a2 2 0 0 1-2.425-1.455z\",\n  key: \"m7xp4m\"\n}], [\"path\", {\n  d: \"m6.158 8.633 1.114 4.456\",\n  key: \"74o979\"\n}], [\"path\", {\n  d: \"m8 21 3.105-6.21\",\n  key: \"1fvxut\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"2\",\n  key: \"1c1ljs\"\n}]];\nconst Telescope = createLucideIcon(\"telescope\", __iconNode);\nexport { __iconNode, Telescope as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Telescope", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/telescope.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm10.065 12.493-6.18 1.318a.934.934 0 0 1-1.108-.702l-.537-2.15a1.07 1.07 0 0 1 .691-1.265l13.504-4.44',\n      key: 'k4qptu',\n    },\n  ],\n  ['path', { d: 'm13.56 11.747 4.332-.924', key: '19l80z' }],\n  ['path', { d: 'm16 21-3.105-6.21', key: '7oh9d' }],\n  [\n    'path',\n    {\n      d: 'M16.485 5.94a2 2 0 0 1 1.455-2.425l1.09-.272a1 1 0 0 1 1.212.727l1.515 6.06a1 1 0 0 1-.727 1.213l-1.09.272a2 2 0 0 1-2.425-1.455z',\n      key: 'm7xp4m',\n    },\n  ],\n  ['path', { d: 'm6.158 8.633 1.114 4.456', key: '74o979' }],\n  ['path', { d: 'm8 21 3.105-6.21', key: '1fvxut' }],\n  ['circle', { cx: '12', cy: '13', r: '2', key: '1c1ljs' }],\n];\n\n/**\n * @component @name Telescope\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAuMDY1IDEyLjQ5My02LjE4IDEuMzE4YS45MzQuOTM0IDAgMCAxLTEuMTA4LS43MDJsLS41MzctMi4xNWExLjA3IDEuMDcgMCAwIDEgLjY5MS0xLjI2NWwxMy41MDQtNC40NCIgLz4KICA8cGF0aCBkPSJtMTMuNTYgMTEuNzQ3IDQuMzMyLS45MjQiIC8+CiAgPHBhdGggZD0ibTE2IDIxLTMuMTA1LTYuMjEiIC8+CiAgPHBhdGggZD0iTTE2LjQ4NSA1Ljk0YTIgMiAwIDAgMSAxLjQ1NS0yLjQyNWwxLjA5LS4yNzJhMSAxIDAgMCAxIDEuMjEyLjcyN2wxLjUxNSA2LjA2YTEgMSAwIDAgMS0uNzI3IDEuMjEzbC0xLjA5LjI3MmEyIDIgMCAwIDEtMi40MjUtMS40NTV6IiAvPgogIDxwYXRoIGQ9Im02LjE1OCA4LjYzMyAxLjExNCA0LjQ1NiIgLz4KICA8cGF0aCBkPSJtOCAyMSAzLjEwNS02LjIxIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTMiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/telescope\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Telescope = createLucideIcon('telescope', __iconNode);\n\nexport default Telescope;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAS,GACjD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,SAAA,GAAYC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}