# 脱敏配置服务

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/your-repo/sanitization-config-service)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![React](https://img.shields.io/badge/React-19.1.0-61dafb.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9.5-blue.svg)](https://www.typescriptlang.org/)

一个基于React的纯前端数据脱敏规则管理系统，采用现代化UI设计，提供完整的脱敏规则管理功能。

## ✨ 核心特性

- 🎨 **现代化UI** - 简洁、直观的用户界面设计
- 📱 **纯前端实现** - 无需后端服务，部署简单
- 💾 **本地存储** - 数据安全存储在浏览器本地
- 🔧 **完整CRUD** - 创建、读取、更新、删除脱敏规则
- 🔄 **批量操作** - 支持批量启用、禁用、删除规则
- 📤 **导入导出** - 配置文件的导入和导出功能
- 🔍 **实时搜索** - 快速查找和过滤规则
- 🎯 **规则验证** - 实时验证规则配置正确性
- 🌐 **响应式设计** - 适配不同屏幕尺寸
- ⚡ **高性能** - 优化的前端性能

## 🚀 快速开始

### 环境要求

- **Node.js** 16.0.0 或更高版本
- **npm** 7.0.0 或更高版本（或 yarn 1.22.0+）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-repo/sanitization-config-service.git
   cd sanitization-config-service
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   # 使用启动脚本
   ./start-frontend.sh
   
   # 或直接使用npm
   npm start
   
   # 或使用开发命令
   npm run dev
   ```

4. **访问应用**
   
   打开浏览器访问: http://localhost:3000

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 本地预览构建结果
npm run serve
```

## 📋 功能概览

### 规则管理
- ✅ 查看所有脱敏规则
- ✅ 创建新的脱敏规则
- ✅ 编辑现有规则
- ✅ 删除不需要的规则
- ✅ 启用/禁用规则状态

### 规则类型
- **字段名匹配** (FIELD_NAME) - 根据字段名进行匹配
- **正则匹配** (PATTERN) - 使用正则表达式匹配内容
- **内容类型** (CONTENT_TYPE) - 根据内容类型匹配
- **自定义** (CUSTOM) - 自定义匹配逻辑

### 严重程度
- 🔴 **关键** (CRITICAL) - 最高优先级，如身份证号
- 🟠 **高** (HIGH) - 高优先级，如手机号
- 🟡 **中等** (MEDIUM) - 中等优先级，如邮箱
- 🟢 **低** (LOW) - 低优先级，如普通文本

### 默认规则
系统预置了三个常用脱敏规则：
- 📱 **手机号脱敏** - 中国手机号格式匹配
- 📧 **邮箱脱敏** - 邮箱地址格式匹配
- 🆔 **身份证脱敏** - 中国身份证号格式匹配

## 🛠️ 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| React | 19.1.0 | 用户界面框架 |
| TypeScript | 4.9.5 | 类型安全的JavaScript |
| Lucide React | 0.525.0 | 现代化图标库 |
| React Hot Toast | 2.5.2 | 消息提示组件 |
| React Scripts | 5.0.1 | 构建和开发工具 |

## 📁 项目结构

```
sanitization-config-service/
├── public/                 # 静态资源
├── src/                   # 源代码
│   ├── components/        # React组件
│   ├── services/         # API服务层
│   ├── types/           # TypeScript类型定义
│   ├── utils/           # 工具函数
│   ├── App.tsx          # 主应用组件
│   ├── index.tsx        # 应用入口
│   └── index.css        # 全局样式
├── build/               # 构建输出
├── DEVELOPMENT.md       # 开发文档
├── DEPLOYMENT.md        # 部署指南
├── USER_GUIDE.md        # 用户指南
├── API.md              # API文档
├── PROJECT_OVERVIEW.md  # 项目总览
├── package.json        # 项目配置
└── README.md           # 项目说明
```

## 📚 文档

- 📖 [用户使用指南](USER_GUIDE.md) - 详细的使用说明
- 🔧 [开发文档](DEVELOPMENT.md) - 开发环境设置和代码结构
- 🚀 [部署指南](DEPLOYMENT.md) - 多种部署方式说明
- 📡 [API文档](API.md) - 完整的API接口说明
- 📊 [项目总览](PROJECT_OVERVIEW.md) - 项目架构和技术细节

## 🎯 使用场景

- **数据隐私保护** - 保护敏感个人信息
- **合规性要求** - 满足数据保护法规
- **开发测试** - 测试环境数据脱敏
- **日志安全** - 日志中敏感信息处理
- **数据分析** - 分析用数据脱敏

## 🔒 数据安全

- **本地存储** - 所有数据存储在用户浏览器本地
- **无网络传输** - 不涉及任何网络数据传输
- **用户控制** - 用户完全控制自己的数据
- **隐私保护** - 不收集任何用户信息

## 🚀 部署选项

### 静态文件服务器
- **Nginx** - 高性能Web服务器
- **Apache** - 传统Web服务器
- **IIS** - Windows服务器

### 云平台部署
- **GitHub Pages** - 免费静态托管
- **Netlify** - 现代化部署平台
- **Vercel** - 前端优化平台
- **AWS S3** - 云存储静态托管

### 容器化部署
- **Docker** - 容器化部署
- **Kubernetes** - 容器编排

详细部署说明请参考 [部署指南](DEPLOYMENT.md)

## 🤝 贡献

我们欢迎所有形式的贡献！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有任何疑问：

- 📋 [提交Issue](https://github.com/your-repo/sanitization-config-service/issues)
- 💬 [讨论区](https://github.com/your-repo/sanitization-config-service/discussions)
- 📧 发送邮件至 <EMAIL>

## 🎉 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**享受简洁高效的脱敏规则管理体验！** 🎉
