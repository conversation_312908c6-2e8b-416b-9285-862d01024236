{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18h10\",\n  key: \"1y5s8o\"\n}], [\"path\", {\n  d: \"m17 21 3-3-3-3\",\n  key: \"1ammt0\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"76md6a\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"v9paqo\"\n}]];\nconst DecimalsArrowRight = createLucideIcon(\"decimals-arrow-right\", __iconNode);\nexport { __iconNode, DecimalsArrowRight as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "DecimalsArrowRight", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/decimals-arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 18h10', key: '1y5s8o' }],\n  ['path', { d: 'm17 21 3-3-3-3', key: '1ammt0' }],\n  ['path', { d: 'M3 11h.01', key: '1eifu7' }],\n  ['rect', { x: '15', y: '3', width: '5', height: '8', rx: '2.5', key: '76md6a' }],\n  ['rect', { x: '6', y: '3', width: '5', height: '8', rx: '2.5', key: 'v9paqo' }],\n];\n\n/**\n * @component @name DecimalsArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMThoMTAiIC8+CiAgPHBhdGggZD0ibTE3IDIxIDMtMy0zLTMiIC8+CiAgPHBhdGggZD0iTTMgMTFoLjAxIiAvPgogIDxyZWN0IHg9IjE1IiB5PSIzIiB3aWR0aD0iNSIgaGVpZ2h0PSI4IiByeD0iMi41IiAvPgogIDxyZWN0IHg9IjYiIHk9IjMiIHdpZHRoPSI1IiBoZWlnaHQ9IjgiIHJ4PSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/decimals-arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DecimalsArrowRight = createLucideIcon('decimals-arrow-right', __iconNode);\n\nexport default DecimalsArrowRight;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAL,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAM,kBAAA,GAAqBC,gBAAiB,yBAAwBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}