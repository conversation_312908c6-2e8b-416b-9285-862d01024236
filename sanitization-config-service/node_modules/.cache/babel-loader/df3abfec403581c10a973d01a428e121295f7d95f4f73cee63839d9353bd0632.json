{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 9 5-5 5 5\",\n  key: \"9ctzwi\"\n}], [\"path\", {\n  d: \"M4 20h7a4 4 0 0 0 4-4V4\",\n  key: \"1plgdj\"\n}]];\nconst CornerRightUp = createLucideIcon(\"corner-right-up\", __iconNode);\nexport { __iconNode, CornerRightUp as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "CornerRightUp", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/corner-right-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10 9 5-5 5 5', key: '9ctzwi' }],\n  ['path', { d: 'M4 20h7a4 4 0 0 0 4-4V4', key: '1plgdj' }],\n];\n\n/**\n * @component @name CornerRightUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAgOSA1LTUgNSA1IiAvPgogIDxwYXRoIGQ9Ik00IDIwaDdhNCA0IDAgMCAwIDQtNFY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/corner-right-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerRightUp = createLucideIcon('corner-right-up', __iconNode);\n\nexport default CornerRightUp;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}