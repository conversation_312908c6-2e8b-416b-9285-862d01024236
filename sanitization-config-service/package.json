{"name": "sanitization-config-service", "version": "1.0.0", "description": "Sanitization Configuration Service - Node.js Implementation", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit"}, "keywords": ["sanitization", "configuration", "security", "data-masking"], "author": "", "license": "ISC", "dependencies": {"fastify": "^4.28.1", "@fastify/cors": "^9.0.1", "@fastify/rate-limit": "^9.1.0", "@fastify/jwt": "^8.0.1", "@fastify/static": "^7.0.4", "ajv": "^8.17.1", "chokidar": "^3.6.0", "pino": "^9.3.2", "pino-pretty": "^11.2.2", "yaml": "^2.5.0", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.14.14", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/ui": "^2.0.5", "eslint": "^8.57.0", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4", "vitest": "^2.0.5"}}