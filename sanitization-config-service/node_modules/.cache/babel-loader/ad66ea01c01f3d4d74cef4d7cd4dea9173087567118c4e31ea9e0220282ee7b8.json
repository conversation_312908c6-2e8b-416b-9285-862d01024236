{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 4V2\",\n  key: \"z1p9b7\"\n}], [\"path\", {\n  d: \"M15 16v-2\",\n  key: \"px0unx\"\n}], [\"path\", {\n  d: \"M8 9h2\",\n  key: \"1g203m\"\n}], [\"path\", {\n  d: \"M20 9h2\",\n  key: \"19tzq7\"\n}], [\"path\", {\n  d: \"M17.8 11.8 19 13\",\n  key: \"yihg8r\"\n}], [\"path\", {\n  d: \"M15 9h.01\",\n  key: \"x1ddxp\"\n}], [\"path\", {\n  d: \"M17.8 6.2 19 5\",\n  key: \"fd4us0\"\n}], [\"path\", {\n  d: \"m3 21 9-9\",\n  key: \"1jfql5\"\n}], [\"path\", {\n  d: \"M12.2 6.2 11 5\",\n  key: \"i3da3b\"\n}]];\nconst Wand = createLucideIcon(\"wand\", __iconNode);\nexport { __iconNode, Wand as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON>d", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/wand.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 4V2', key: 'z1p9b7' }],\n  ['path', { d: 'M15 16v-2', key: 'px0unx' }],\n  ['path', { d: 'M8 9h2', key: '1g203m' }],\n  ['path', { d: 'M20 9h2', key: '19tzq7' }],\n  ['path', { d: 'M17.8 11.8 19 13', key: 'yihg8r' }],\n  ['path', { d: 'M15 9h.01', key: 'x1ddxp' }],\n  ['path', { d: 'M17.8 6.2 19 5', key: 'fd4us0' }],\n  ['path', { d: 'm3 21 9-9', key: '1jfql5' }],\n  ['path', { d: 'M12.2 6.2 11 5', key: 'i3da3b' }],\n];\n\n/**\n * @component @name Wand\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgNFYyIiAvPgogIDxwYXRoIGQ9Ik0xNSAxNnYtMiIgLz4KICA8cGF0aCBkPSJNOCA5aDIiIC8+CiAgPHBhdGggZD0iTTIwIDloMiIgLz4KICA8cGF0aCBkPSJNMTcuOCAxMS44IDE5IDEzIiAvPgogIDxwYXRoIGQ9Ik0xNSA5aC4wMSIgLz4KICA8cGF0aCBkPSJNMTcuOCA2LjIgMTkgNSIgLz4KICA8cGF0aCBkPSJtMyAyMSA5LTkiIC8+CiAgPHBhdGggZD0iTTEyLjIgNi4yIDExIDUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wand\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wand = createLucideIcon('wand', __iconNode);\n\nexport default Wand;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAU,GACjD;AAaM,MAAAC,IAAA,GAAOC,gBAAiB,SAAQJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}