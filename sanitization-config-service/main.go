package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"sanitization-config-service/config"
	"sanitization-config-service/handlers"
	"sanitization-config-service/middleware"
	"sanitization-config-service/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Set Gin mode
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize services
	ruleService := service.NewRuleService(cfg.Rules.ConfigFile)

	// Start config reloader if enabled
	if cfg.Rules.ReloadEnabled {
		go startConfigReloader(ruleService, cfg.Rules.ReloadInterval)
	}

	// Initialize handlers
	rulesHandler := handlers.NewRulesHandler(ruleService)

	// Setup router
	router := setupRouter(cfg, rulesHandler)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on %s:%d", cfg.Server.Host, cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

func setupRouter(cfg *config.Config, rulesHandler *handlers.RulesHandler) *gin.Engine {
	router := gin.New()

	// Add middleware
	router.Use(middleware.LoggingMiddleware())
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.RateLimitMiddleware())
	router.Use(gin.Recovery())

	// Health check endpoint (no auth required)
	router.GET("/health", rulesHandler.GetHealth)

	// API routes
	api := router.Group("/api")

	// Add authentication middleware if enabled
	authConfig := middleware.AuthConfig{
		Enabled:     cfg.Auth.Enabled,
		JWTSecret:   cfg.Auth.JWTSecret,
		TokenHeader: cfg.Auth.TokenHeader,
	}
	api.Use(middleware.AuthMiddleware(authConfig))

	// Sanitization routes
	sanitization := api.Group("/sanitization")
	{
		// Rule management
		sanitization.GET("/rules", rulesHandler.GetRules)
		sanitization.POST("/rules", rulesHandler.CreateRule)
		sanitization.PUT("/rules/:id", rulesHandler.UpdateRule)
		sanitization.DELETE("/rules/:id", rulesHandler.DeleteRule)
		sanitization.POST("/rules/:id/toggle", rulesHandler.ToggleRule)

		// Batch operations
		sanitization.POST("/rules/batch", rulesHandler.BatchOperation)

		// Rule validation
		sanitization.POST("/rules/validate", rulesHandler.ValidateRule)

		// Import/Export
		sanitization.GET("/rules/export", rulesHandler.ExportRules)
		sanitization.POST("/rules/import", rulesHandler.ImportRules)

		// Configuration management
		sanitization.POST("/rules/reload", rulesHandler.ReloadRules)
		sanitization.POST("/toggle", rulesHandler.ToggleGlobalSwitch)
	}

	// Metrics endpoint
	router.GET("/metrics", rulesHandler.GetMetrics)

	// Root endpoint
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service":   "Sanitization Config Service",
			"version":   "1.0.0",
			"timestamp": time.Now().Unix(),
			"endpoints": []string{
				"GET /health - Health check",
				"GET /metrics - Service metrics",
				"GET /api/sanitization/rules - Get sanitization rules",
				"POST /api/sanitization/rules - Create new rule",
				"PUT /api/sanitization/rules/:id - Update existing rule",
				"DELETE /api/sanitization/rules/:id - Delete rule",
				"POST /api/sanitization/rules/:id/toggle - Toggle rule status",
				"POST /api/sanitization/rules/batch - Batch operations on rules",
				"POST /api/sanitization/rules/validate - Validate rule configuration",
				"POST /api/sanitization/rules/reload - Reload rules from config file",
				"POST /api/sanitization/toggle - Toggle global sanitization switch",
			},
		})
	})

	return router
}

func startConfigReloader(ruleService *service.RuleService, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	log.Printf("Started config reloader with interval: %v", interval)

	for {
		select {
		case <-ticker.C:
			if err := ruleService.ReloadConfig(); err != nil {
				log.Printf("Failed to reload config: %v", err)
			} else {
				log.Println("Config reloaded successfully")
			}
		}
	}
}
