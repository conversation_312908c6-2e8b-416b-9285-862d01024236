{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 15 6 6\",\n  key: \"1s409w\"\n}], [\"path\", {\n  d: \"m15 9 6-6\",\n  key: \"ko1vev\"\n}], [\"path\", {\n  d: \"M21 16v5h-5\",\n  key: \"1ck2sf\"\n}], [\"path\", {\n  d: \"M21 8V3h-5\",\n  key: \"1qoq8a\"\n}], [\"path\", {\n  d: \"M3 16v5h5\",\n  key: \"1t08am\"\n}], [\"path\", {\n  d: \"m3 21 6-6\",\n  key: \"wwnumi\"\n}], [\"path\", {\n  d: \"M3 8V3h5\",\n  key: \"1ln10m\"\n}], [\"path\", {\n  d: \"M9 9 3 3\",\n  key: \"v551iv\"\n}]];\nconst Expand = createLucideIcon(\"expand\", __iconNode);\nexport { __iconNode, Expand as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Expand", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/expand.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm15 15 6 6', key: '1s409w' }],\n  ['path', { d: 'm15 9 6-6', key: 'ko1vev' }],\n  ['path', { d: 'M21 16v5h-5', key: '1ck2sf' }],\n  ['path', { d: 'M21 8V3h-5', key: '1qoq8a' }],\n  ['path', { d: 'M3 16v5h5', key: '1t08am' }],\n  ['path', { d: 'm3 21 6-6', key: 'wwnumi' }],\n  ['path', { d: 'M3 8V3h5', key: '1ln10m' }],\n  ['path', { d: 'M9 9 3 3', key: 'v551iv' }],\n];\n\n/**\n * @component @name Expand\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTUgNiA2IiAvPgogIDxwYXRoIGQ9Im0xNSA5IDYtNiIgLz4KICA8cGF0aCBkPSJNMjEgMTZ2NWgtNSIgLz4KICA8cGF0aCBkPSJNMjEgOFYzaC01IiAvPgogIDxwYXRoIGQ9Ik0zIDE2djVoNSIgLz4KICA8cGF0aCBkPSJtMyAyMSA2LTYiIC8+CiAgPHBhdGggZD0iTTMgOFYzaDUiIC8+CiAgPHBhdGggZD0iTTkgOSAzIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/expand\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Expand = createLucideIcon('expand', __iconNode);\n\nexport default Expand;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}