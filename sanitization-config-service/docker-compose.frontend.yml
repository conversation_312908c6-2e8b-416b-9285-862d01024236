version: '3.8'

services:
  sanitization-backend:
    build: .
    ports:
      - "8081:8080"
    environment:
      - SERVER_PORT=8080
      - RULES_CONFIG_FILE=config/rules.json
    volumes:
      - ./config:/app/config
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  sanitization-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8081
    depends_on:
      - sanitization-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: sanitization-network
