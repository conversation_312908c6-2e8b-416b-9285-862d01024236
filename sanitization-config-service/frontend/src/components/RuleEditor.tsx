import React, { useState, useEffect } from 'react';
import { X, Save, TestTube, AlertCircle } from 'lucide-react';
import { SanitizationRule, RuleType, SeverityLevel } from '../types';
import { sanitizationApi } from '../services/api';
import toast from 'react-hot-toast';

interface RuleEditorProps {
  rule?: SanitizationRule;
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: SanitizationRule) => void;
  mode: 'create' | 'edit';
}

const RuleEditor: React.FC<RuleEditorProps> = ({ rule, isOpen, onClose, onSave, mode }) => {
  const [formData, setFormData] = useState<Partial<SanitizationRule>>({
    id: '',
    name: '',
    description: '',
    type: 'FIELD_NAME' as RuleType,
    severity: 'MEDIUM' as SeverityLevel,
    enabled: true,
    priority: 100,
    fieldNames: [],
    pattern: '',
    contentTypes: [],
    maskValue: '****',
    markerType: '',
    preserveFormat: false,
    preserveLength: 0,
    includeServices: [],
    excludeServices: [],
    conditions: {}
  });

  const [testInput, setTestInput] = useState('');
  const [testOutput, setTestOutput] = useState('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (rule && mode === 'edit') {
      setFormData(rule);
    } else if (mode === 'create') {
      setFormData({
        id: '',
        name: '',
        description: '',
        type: 'FIELD_NAME' as RuleType,
        severity: 'MEDIUM' as SeverityLevel,
        enabled: true,
        priority: 100,
        fieldNames: [],
        pattern: '',
        contentTypes: [],
        maskValue: '****',
        markerType: '',
        preserveFormat: false,
        preserveLength: 0,
        includeServices: [],
        excludeServices: [],
        conditions: {}
      });
    }
  }, [rule, mode]);

  const handleInputChange = (field: keyof SanitizationRule, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setValidationErrors([]);
  };

  const handleArrayInputChange = (field: keyof SanitizationRule, value: string) => {
    const array = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({ ...prev, [field]: array }));
  };

  const validateRule = async () => {
    if (!formData.id || !formData.name || !formData.type || !formData.maskValue) {
      setValidationErrors(['Please fill in all required fields']);
      return false;
    }

    setIsValidating(true);
    try {
      const response = await sanitizationApi.validateRule(formData as SanitizationRule, testInput);
      setValidationErrors(response.errors || []);
      setTestOutput(response.testOutput || '');
      return response.valid;
    } catch (error) {
      setValidationErrors(['Validation failed']);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleSave = async () => {
    const isValid = await validateRule();
    if (!isValid) {
      toast.error('Please fix validation errors before saving');
      return;
    }

    setIsSaving(true);
    try {
      if (mode === 'create') {
        await sanitizationApi.createRule(formData as SanitizationRule);
        toast.success('Rule created successfully');
      } else {
        await sanitizationApi.updateRule(formData.id!, formData as SanitizationRule);
        toast.success('Rule updated successfully');
      }
      onSave(formData as SanitizationRule);
      onClose();
    } catch (error) {
      toast.error(`Failed to ${mode} rule`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    await validateRule();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-xl">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              {mode === 'create' ? '新建脱敏规则' : '编辑脱敏规则'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="p-8 space-y-8">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                规则ID *
              </label>
              <input
                type="text"
                value={formData.id}
                onChange={(e) => handleInputChange('id', e.target.value)}
                disabled={mode === 'edit'}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:bg-gray-100 disabled:text-gray-500"
                placeholder="unique-rule-id"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                规则名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                placeholder="规则显示名称"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              规则描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
              className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200 resize-none"
              placeholder="描述此规则的作用..."
            />
          </div>

          {/* Rule Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                规则类型 *
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value as RuleType)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              >
                <option value="FIELD_NAME">字段名匹配</option>
                <option value="PATTERN">正则匹配</option>
                <option value="CONTENT_TYPE">内容类型</option>
                <option value="CUSTOM">自定义</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                严重程度 *
              </label>
              <select
                value={formData.severity}
                onChange={(e) => handleInputChange('severity', e.target.value as SeverityLevel)}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              >
                <option value="LOW">低</option>
                <option value="MEDIUM">中等</option>
                <option value="HIGH">高</option>
                <option value="CRITICAL">关键</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                优先级
              </label>
              <input
                type="number"
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                className="w-full px-4 py-3 border-0 rounded-xl text-gray-900 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                min="1"
                max="1000"
              />
            </div>
          </div>

          {/* Type-specific fields */}
          {formData.type === 'FIELD_NAME' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Field Names (comma-separated) *
              </label>
              <input
                type="text"
                value={formData.fieldNames?.join(', ') || ''}
                onChange={(e) => handleArrayInputChange('fieldNames', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="password, secret, token"
              />
            </div>
          )}

          {formData.type === 'PATTERN' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Regex Pattern *
              </label>
              <input
                type="text"
                value={formData.pattern}
                onChange={(e) => handleInputChange('pattern', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                placeholder="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Mask Value *
            </label>
            <input
              type="text"
              value={formData.maskValue}
              onChange={(e) => handleInputChange('maskValue', e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="****"
            />
          </div>

          {/* Test Section */}
          <div className="border-t border-gray-800 pt-6">
            <h3 className="text-lg font-semibold text-white mb-4">Test Rule</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Test Input
                </label>
                <textarea
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                  placeholder="Enter test data here..."
                />
              </div>
              <button
                onClick={handleTest}
                disabled={isValidating}
                className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <TestTube className="h-4 w-4 mr-2" />
                {isValidating ? 'Testing...' : 'Test Rule'}
              </button>
              {testOutput && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Test Output
                  </label>
                  <div className="p-3 bg-gray-800 border border-gray-700 rounded-lg text-green-400 font-mono">
                    {testOutput}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                <h4 className="text-red-400 font-medium">Validation Errors</h4>
              </div>
              <ul className="text-red-300 text-sm space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-100 p-8 rounded-b-lg">
          <div className="flex items-center justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors rounded-xl hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving || validationErrors.length > 0}
              className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-semibold transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? '保存中...' : '保存规则'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RuleEditor;
