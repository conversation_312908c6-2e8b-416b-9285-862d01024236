import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import {
  Settings,
  RefreshCw,
  Power,
  PowerOff
} from 'lucide-react';
import { sanitizationApi } from './services/api';
import { SanitizationConfig, SanitizationRule } from './types';
import RulesList from './components/RulesList';
import RuleEditor from './components/RuleEditor';
import toast from 'react-hot-toast';

function App() {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [toggling, setToggling] = useState(false);

  // Rule Editor State
  const [ruleEditorOpen, setRuleEditorOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<SanitizationRule | undefined>();
  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchRules();
  }, []);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('Failed to fetch rules');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('Rules reloaded successfully');
      await fetchRules();
    } catch (error) {
      toast.error('Failed to reload rules');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);
      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? 'Global sanitization enabled' : 'Global sanitization disabled');
    } catch (error) {
      toast.error('Failed to toggle global switch');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  const handleCreateRule = () => {
    setEditingRule(undefined);
    setEditorMode('create');
    setRuleEditorOpen(true);
  };

  const handleEditRule = (rule: SanitizationRule) => {
    setEditingRule(rule);
    setEditorMode('edit');
    setRuleEditorOpen(true);
  };

  const handleRuleSaved = () => {
    fetchRules();
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
            <Settings className="h-8 w-8 text-white" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Twitter-style Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  脱敏规则管理
                </h1>
              </div>
            </div>

            {/* Global Controls */}
            <div className="flex items-center space-x-4">
              {/* Global Status */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">全局脱敏</span>
                <div className={`flex items-center space-x-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${
                  config?.enabled
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-1.5 h-1.5 rounded-full ${
                    config?.enabled ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span>
                    {config?.enabled ? '已启用' : '已禁用'}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleReload}
                  className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-1 inline" />
                  重载
                </button>
                <button
                  onClick={handleToggleGlobal}
                  disabled={toggling}
                  className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
                    config?.enabled
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {config?.enabled ? <PowerOff className="h-4 w-4 mr-1 inline" /> : <Power className="h-4 w-4 mr-1 inline" />}
                  {toggling ? '处理中...' : (config?.enabled ? '禁用' : '启用')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-6">
        <RulesList
          config={config}
          onRefresh={fetchRules}
          onEditRule={handleEditRule}
          onCreateRule={handleCreateRule}
        />
      </main>

      {/* Rule Editor Modal */}
      <RuleEditor
        rule={editingRule}
        isOpen={ruleEditorOpen}
        onClose={() => setRuleEditorOpen(false)}
        onSave={handleRuleSaved}
        mode={editorMode}
      />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            color: '#f3f4f6',
            border: '1px solid #374151',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#1f2937',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#1f2937',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
