import { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import {
  Shield,
  Search,
  Download,
  Trash2,
  Eye,
  EyeOff,
  Power,
  PowerOff
} from 'lucide-react';
import { sanitizationApi } from './services/api';
import { SanitizationConfig } from './types';
import toast from 'react-hot-toast';

function App() {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [globalEnabled, setGlobalEnabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchRules();
  }, []);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const data = await sanitizationApi.getRules();
      setConfig(data);
      setGlobalEnabled(data.enabled);
    } catch (error) {
      toast.error('Failed to fetch rules');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleGlobalSwitch = async () => {
    try {
      const newState = !globalEnabled;
      await sanitizationApi.toggleGlobalSwitch(newState);
      setGlobalEnabled(newState);
      toast.success(`Global sanitization ${newState ? 'enabled' : 'disabled'}`);
    } catch (error) {
      toast.error('Failed to toggle global switch');
    }
  };

  const toggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await sanitizationApi.toggleRule(ruleId, enabled);
      await fetchRules();
      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      toast.error('Failed to toggle rule');
    }
  };

  const deleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }
    
    try {
      await sanitizationApi.deleteRule(ruleId);
      await fetchRules();
      toast.success('Rule deleted successfully');
    } catch (error) {
      toast.error('Failed to delete rule');
    }
  };

  const exportRules = async () => {
    try {
      const data = await sanitizationApi.exportRules();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sanitization-rules.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Rules exported successfully');
    } catch (error) {
      toast.error('Failed to export rules');
    }
  };

  const filteredRules = config?.rules.filter(rule =>
    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.id.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 bg-red-50';
      case 'HIGH': return 'text-orange-600 bg-orange-50';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50';
      case 'LOW': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading sanitization rules...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />
      
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Data Sanitization Rules</h1>
                <p className="text-sm text-gray-600">Manage your data privacy and security rules</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleGlobalSwitch}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  globalEnabled
                    ? 'bg-green-100 text-green-700 hover:bg-green-200'
                    : 'bg-red-100 text-red-700 hover:bg-red-200'
                }`}
              >
                {globalEnabled ? <Power className="h-4 w-4" /> : <PowerOff className="h-4 w-4" />}
                <span>{globalEnabled ? 'Enabled' : 'Disabled'}</span>
              </button>
              
              <button
                onClick={exportRules}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Search and Stats */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search rules..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div>Total: <span className="font-semibold text-gray-900">{config?.rules.length || 0}</span></div>
              <div>Active: <span className="font-semibold text-green-600">{config?.rules.filter(r => r.enabled).length || 0}</span></div>
              <div>Inactive: <span className="font-semibold text-red-600">{config?.rules.filter(r => !r.enabled).length || 0}</span></div>
            </div>
          </div>
        </div>

        {/* Rules List */}
        <div className="space-y-4">
          {filteredRules.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No rules found</h3>
              <p className="text-gray-600">
                {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first sanitization rule.'}
              </p>
            </div>
          ) : (
            filteredRules.map((rule) => (
              <div key={rule.id} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{rule.name}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(rule.severity)}`}>
                        {rule.severity}
                      </span>
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full">
                        {rule.type}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{rule.description}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>ID: {rule.id}</span>
                      {rule.pattern && <span>Pattern: {rule.pattern}</span>}
                      {rule.fieldNames && rule.fieldNames.length > 0 && (
                        <span>Fields: {rule.fieldNames.join(', ')}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => toggleRule(rule.id, !rule.enabled)}
                      className={`p-2 rounded-lg transition-colors ${
                        rule.enabled
                          ? 'text-green-600 hover:bg-green-50'
                          : 'text-gray-400 hover:bg-gray-50'
                      }`}
                      title={rule.enabled ? 'Disable rule' : 'Enable rule'}
                    >
                      {rule.enabled ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </button>
                    
                    <button
                      onClick={() => deleteRule(rule.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete rule"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
