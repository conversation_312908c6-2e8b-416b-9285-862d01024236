{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"8\",\n  key: \"46899m\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"6\",\n  y1: \"3\",\n  y2: \"6\",\n  key: \"1jkytn\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"18\",\n  y1: \"3\",\n  y2: \"6\",\n  key: \"14zfjt\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"6\",\n  y1: \"21\",\n  y2: \"18\",\n  key: \"iusuec\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"18\",\n  y1: \"21\",\n  y2: \"18\",\n  key: \"yj2dd7\"\n}]];\nconst Currency = createLucideIcon(\"currency\", __iconNode);\nexport { __iconNode, Currency as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/node_modules/lucide-react/src/icons/currency.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '8', key: '46899m' }],\n  ['line', { x1: '3', x2: '6', y1: '3', y2: '6', key: '1jkytn' }],\n  ['line', { x1: '21', x2: '18', y1: '3', y2: '6', key: '14zfjt' }],\n  ['line', { x1: '3', x2: '6', y1: '21', y2: '18', key: 'iusuec' }],\n  ['line', { x1: '21', x2: '18', y1: '21', y2: '18', key: 'yj2dd7' }],\n];\n\n/**\n * @component @name Currency\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI4IiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iNiIgeTE9IjMiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIyMSIgeDI9IjE4IiB5MT0iMyIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjMiIHgyPSI2IiB5MT0iMjEiIHkyPSIxOCIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSIxOCIgeTE9IjIxIiB5Mj0iMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/currency\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Currency = createLucideIcon('currency', __iconNode);\n\nexport default Currency;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GACpE;AAaM,MAAAK,QAAA,GAAWC,gBAAiB,aAAYV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}