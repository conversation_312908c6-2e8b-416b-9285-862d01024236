[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts": "4", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/components/RuleEditor.tsx": "5"}, {"size": 554, "mtime": 1752811811415, "results": "6", "hashOfConfig": "7"}, {"size": 425, "mtime": 1752811811418, "results": "8", "hashOfConfig": "7"}, {"size": 9809, "mtime": 1752812546963, "results": "9", "hashOfConfig": "7"}, {"size": 12242, "mtime": 1752811955464, "results": "10", "hashOfConfig": "7"}, {"size": 13926, "mtime": 1752811811419, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jk5nvp", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/App.tsx", ["27", "28", "29", "30", "31"], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/services/api.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/src/components/RuleEditor.tsx", [], [], {"ruleId": "32", "severity": 1, "message": "33", "line": 5, "column": 3, "nodeType": "34", "messageId": "35", "endLine": 5, "endColumn": 7}, {"ruleId": "32", "severity": 1, "message": "36", "line": 8, "column": 3, "nodeType": "34", "messageId": "35", "endLine": 8, "endColumn": 9}, {"ruleId": "32", "severity": 1, "message": "37", "line": 9, "column": 3, "nodeType": "34", "messageId": "35", "endLine": 9, "endColumn": 7}, {"ruleId": "32", "severity": 1, "message": "38", "line": 15, "column": 3, "nodeType": "34", "messageId": "35", "endLine": 15, "endColumn": 11}, {"ruleId": "32", "severity": 1, "message": "39", "line": 18, "column": 30, "nodeType": "34", "messageId": "35", "endLine": 18, "endColumn": 46}, "@typescript-eslint/no-unused-vars", "'Plus' is defined but never used.", "Identifier", "unusedVar", "'Upload' is defined but never used.", "'Edit' is defined but never used.", "'Settings' is defined but never used.", "'SanitizationRule' is defined but never used."]