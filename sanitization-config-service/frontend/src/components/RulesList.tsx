import React, { useState } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Power,
  PowerOff,
  Filter,
  MoreVertical,
  CheckSquare,
  Square,
  Download,
  Upload
} from 'lucide-react';
import { SanitizationRule, SanitizationConfig } from '../types';
import { sanitizationApi } from '../services/api';
import toast from 'react-hot-toast';

interface RulesListProps {
  config: SanitizationConfig | null;
  onRefresh: () => void;
  onEditRule: (rule: SanitizationRule) => void;
  onCreateRule: () => void;
}

const RulesList: React.FC<RulesListProps> = ({ config, onRefresh, onEditRule, onCreateRule }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  const rules = config?.rules || [];

  // Filter rules based on search and filters
  const filteredRules = rules.filter(rule => {
    const matchesSearch = !searchTerm || 
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || rule.type === filterType;
    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'enabled' && rule.enabled) ||
      (filterStatus === 'disabled' && !rule.enabled);

    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  });

  const handleSelectRule = (ruleId: string) => {
    const newSelected = new Set(selectedRules);
    if (newSelected.has(ruleId)) {
      newSelected.delete(ruleId);
    } else {
      newSelected.add(ruleId);
    }
    setSelectedRules(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRules.size === filteredRules.length) {
      setSelectedRules(new Set());
    } else {
      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await sanitizationApi.toggleRule(ruleId, enabled);
      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);
      onRefresh();
    } catch (error) {
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }

    try {
      await sanitizationApi.deleteRule(ruleId);
      toast.success('Rule deleted successfully');
      onRefresh();
    } catch (error) {
      toast.error('Failed to delete rule');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';
      case 'PATTERN': return 'bg-purple-100 text-purple-800';
      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';
      case 'CUSTOM': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleExportRules = async () => {
    try {
      const response = await sanitizationApi.getRules();
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sanitization-rules.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Configuration exported successfully');
    } catch (error) {
      toast.error('Failed to export configuration');
    }
  };

  const handleImportRules = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const config = JSON.parse(text);
      await sanitizationApi.importRules(config);
      toast.success('Configuration imported successfully');
      onRefresh();
    } catch (error) {
      toast.error('Failed to import configuration');
    }
    event.target.value = '';
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              脱敏规则
            </h2>
            <p className="text-gray-500 text-sm mt-1">
              共 {config?.rules.length || 0} 条规则，显示 {filteredRules.length} 条
              {selectedRules.size > 0 && (
                <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                  已选择 {selectedRules.size} 条
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportRules}
              className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Download className="h-4 w-4 mr-1 inline" />
              导出
            </button>
            <label className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors cursor-pointer">
              <Upload className="h-4 w-4 mr-1 inline" />
              导入
              <input
                type="file"
                accept=".json"
                onChange={handleImportRules}
                className="hidden"
              />
            </label>
            <button
              onClick={onCreateRule}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-full text-sm transition-colors"
            >
              <Plus className="h-4 w-4 mr-1.5 inline" />
              新建规则
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索规则..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full text-sm text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-3 py-2 rounded-full text-sm font-medium transition-colors ${
              showFilters
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <Filter className="h-4 w-4 mr-1 inline" />
            筛选
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">规则类型</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
              >
                <option value="all">全部类型</option>
                <option value="FIELD_NAME">字段名匹配</option>
                <option value="PATTERN">正则匹配</option>
                <option value="CONTENT_TYPE">内容类型</option>
                <option value="CUSTOM">自定义</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">严重程度</label>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
              >
                <option value="all">全部级别</option>
                <option value="CRITICAL">关键</option>
                <option value="HIGH">高</option>
                <option value="MEDIUM">中等</option>
                <option value="LOW">低</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
              >
                <option value="all">全部状态</option>
                <option value="enabled">已启用</option>
                <option value="disabled">已禁用</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Batch Actions */}
      {selectedRules.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CheckSquare className="h-4 w-4 text-blue-600" />
              <span className="text-gray-800 font-medium text-sm">
                已选择 {selectedRules.size} 条规则
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded-full text-sm font-medium transition-colors">
                <Power className="h-4 w-4 mr-1 inline" />
                启用
              </button>
              <button className="px-3 py-1.5 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full text-sm font-medium transition-colors">
                <PowerOff className="h-4 w-4 mr-1 inline" />
                禁用
              </button>
              <button className="px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-full text-sm font-medium transition-colors">
                <Trash2 className="h-4 w-4 mr-1 inline" />
                删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="space-y-3">
        {filteredRules.length === 0 ? (
          <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">未找到规则</h3>
            <p className="text-sm text-gray-500 max-w-sm mx-auto">
              {searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all'
                ? '请尝试调整搜索条件或筛选器'
                : '创建您的第一个脱敏规则开始使用'
              }
            </p>
          </div>
        ) : (
          <div>
            {/* Select All */}
            <div className="bg-white rounded-lg border border-gray-200 p-3">
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSelectAll}
                  className="text-gray-400 hover:text-blue-600 transition-colors p-1 rounded hover:bg-blue-50"
                >
                  {selectedRules.size === filteredRules.length ? (
                    <CheckSquare className="h-4 w-4" />
                  ) : (
                    <Square className="h-4 w-4" />
                  )}
                </button>
                <span className="text-gray-700 font-medium text-sm">
                  全选 {filteredRules.length} 条规则
                </span>
              </div>
            </div>

            {/* Rules */}
            {filteredRules.map((rule) => (
              <div
                key={rule.id}
                className={`bg-white rounded-lg border transition-all hover:shadow-md ${
                  selectedRules.has(rule.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <button
                        onClick={() => handleSelectRule(rule.id)}
                        className="mt-1 text-gray-400 hover:text-blue-600 transition-colors p-1 rounded hover:bg-blue-50"
                      >
                        {selectedRules.has(rule.id) ? (
                          <CheckSquare className="h-4 w-4" />
                        ) : (
                          <Square className="h-4 w-4" />
                        )}
                      </button>

                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="text-lg font-semibold text-gray-900">{rule.name}</h4>
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                            {rule.severity === 'CRITICAL' ? '关键' :
                             rule.severity === 'HIGH' ? '高' :
                             rule.severity === 'MEDIUM' ? '中等' : '低'}
                          </span>
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                            {rule.type === 'FIELD_NAME' ? '字段匹配' :
                             rule.type === 'PATTERN' ? '正则匹配' :
                             rule.type === 'CONTENT_TYPE' ? '内容类型' : '自定义'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
                        <div className="text-xs text-gray-500 mb-3">规则ID: {rule.id}</div>
                        
                        <div className="space-y-2">
                          {rule.fieldNames && rule.fieldNames.length > 0 && (
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-gray-700 text-xs">匹配字段:</span>
                              <div className="flex flex-wrap gap-1">
                                {rule.fieldNames.map((field, index) => (
                                  <span key={index} className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">
                                    {field}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                          {rule.pattern && (
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-gray-700 text-xs">正则表达式:</span>
                              <code className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-mono">
                                {rule.pattern}
                              </code>
                            </div>
                          )}
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-700 text-xs">脱敏值:</span>
                            <code className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-mono">
                              {rule.maskValue}
                            </code>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        rule.enabled
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                          rule.enabled ? 'bg-green-500' : 'bg-gray-400'
                        }`}></div>
                        <span>
                          {rule.enabled ? '已启用' : '已禁用'}
                        </span>
                      </div>

                      <div className="relative">
                        <button
                          onClick={() => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id)}
                          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                        >
                          <MoreVertical className="h-4 w-4 text-gray-500" />
                        </button>

                        {actionMenuOpen === rule.id && (
                          <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                            <button
                              onClick={() => {
                                onEditRule(rule);
                                setActionMenuOpen(null);
                              }}
                              className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2 text-sm rounded-t-lg transition-colors"
                            >
                              <Edit className="h-4 w-4" />
                              <span>编辑</span>
                            </button>
                            <button
                              onClick={() => {
                                handleToggleRule(rule.id, !rule.enabled);
                                setActionMenuOpen(null);
                              }}
                              className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2 text-sm transition-colors"
                            >
                              {rule.enabled ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
                              <span>{rule.enabled ? '禁用' : '启用'}</span>
                            </button>
                            <button
                              onClick={() => {
                                handleDeleteRule(rule.id);
                                setActionMenuOpen(null);
                              }}
                              className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm rounded-b-lg transition-colors"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>删除</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RulesList;
